{"name": "fh-admin", "private": true, "scripts": {"dev:build": "NODE_ENV=development webpack --env=dev", "dev:build:watch": "NODE_ENV=development webpack --env=dev --watch", "prod:build": "NODE_ENV=production webpack"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@freshheads/webpack-config-builder": "^5.1.0", "autoprefixer": "^10.0.0", "babel-loader": "^9.1.3", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^5.0.1", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.14", "postcss-loader": "^7.3.3", "resolve-url-loader": "^5.0.0", "sass": "^1.52.1", "sass-loader": "^13.3.2", "webpack": "^5.72.1", "webpack-cli": "^5.1.4", "webpack-stats-plugin": "^1.0.3"}, "dependencies": {"core-js": "^3.33.1"}}