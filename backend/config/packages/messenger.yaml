framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        # failure_transport: failed

        default_bus: default
        buses:
            command_bus:
                middleware:
                    - doctrine_transaction
            default:
                middleware:
                    - validation

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    max_retries: 3
                    # milliseconds delay
                    delay: 1000
                    # causes the delay to be higher before each retry
                    multiplier: 2
                    max_delay: 0
                    # applies randomness to the delay that can prevent the thundering herd effect
                    jitter: 0.1

            # failed: 'doctrine://default?queue_name=failed'
            sync: 'sync://'

        routing:
            # Route your messages to the transports
            'FH\MixpanelBundle\Messenger\TrackEvent': async
            'Symfony\Component\Mailer\Messenger\SendEmailMessage': async

when@dev:
    framework:
        messenger:
            transports:
                async: 'sync://'

when@test:
    framework:
        messenger:
            transports:
                # replace with your transport name here (e.g., my_transport: 'in-memory://')
                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
                async: 'in-memory://'
