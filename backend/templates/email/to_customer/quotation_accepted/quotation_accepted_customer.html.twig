<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><!--$-->
<html dir="ltr" lang="en">

  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
    <style>
      @media(min-width:768px) {
        .md_text-center {
          text-align: center !important
        }
      }
    </style>
  </head>

  <body style="background-color:rgb(250,251,251);font-size:16px;line-height:24px;font-family:Arial">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(255,255,255);padding:16px;max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td><img alt="" src="{{theme.logoUrl}}" style="margin-left:auto;margin-right:auto;margin-bottom:20px;margin-top:0;width:50%;display:block;outline:none;border:none;text-decoration:none" />
            <hr style="margin-top:16px;margin-bottom:16px;margin-left:0;margin-right:0;background-color:rgb(226,232,240);width:100%;border:none;border-top:1px solid #eaeaea" />
            <h1 class="md_text-center" style="text-align:left;margin-top:0;margin-bottom:0;line-height:32px;font-size:24px;margin:0">{{ 'email.quotation_accepted_customer.title' | trans }}</h1>
            <p style="font-size:14px;line-height:20px;margin:16px 0">{{ 'email.quotation_accepted_customer.salutation' | trans({'%customer_name%': customer.fullName}) }}</p>
            <p style="font-size:14px;line-height:20px;margin:16px 0">{{ 'email.quotation_accepted_customer.intro_text' | trans({'%quotation_number%': quotation.formattedNumber}) }}</p>
            <p style="font-size:14px;line-height:20px;margin:16px 0">{{ 'email.quotation_accepted_customer.main_text' | trans }}</p>
            <p style="font-size:14px;line-height:20px;margin:16px 0">{{ 'email.quotation_accepted_customer.support_text' | trans }}</p>
            <h1 style="text-align:left;margin-bottom:0;line-height:28px;font-size:20px">{{ 'email.quotation.follow_up_title' | trans }}</h1>
            <ul style="list-style-position:outside;padding-left:20px;margin-top:0">{% for title, text in followUpLines %}<li style="margin-top:8px">
                <p style="margin-top:0;margin-bottom:0;margin:0;font-weight:700;font-size:14px;line-height:24px">{{title}}</p>
                <p style="margin-top:0;margin-bottom:0;margin:0;font-size:14px;line-height:24px">{{text}}</p>
              </li>{% endfor %}</ul>
            <p style="white-space:pre-line;font-size:14px;line-height:24px;margin:16px 0">{{ 'email.quotation_accepted_customer.outro_text' | trans }}</p>
            <p style="margin-top:0;margin-bottom:0;margin:0;font-size:14px;line-height:24px">{{ 'email.quotation_accepted_customer.closure' | trans }}</p>
            <p style="margin-top:0;margin-bottom:0;margin:0;font-size:14px;line-height:24px">{{ contractor.user.fullName }}</p>
            <p style="margin-top:0;margin-bottom:0;margin:0;font-size:14px;line-height:24px">{{ contractor.name }}</p>
          </td>
        </tr>
      </tbody>
    </table>
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(230,231,231);padding:24px;max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="margin-bottom:16px">
                      <tbody style="width:100%">
                        <tr style="width:100%">{% for element in titleLines %}<p style="font-size:18px;line-height:1;font-weight:700;margin:0;margin-bottom:8px">{{element}}</p>{% endfor %}</tr>
                      </tbody>
                    </table>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="margin-top:24px">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column" style="width:auto;line-height:20px;font-size:14px;vertical-align:middle"><img width="12px" height="16px" src="{{ absolute_url(asset('fa-mobile-black.png', 'email')) }}" alt="" style="margin-right:8px;vertical-align:middle" />{{ footer_phone_number }}</td>
                          <td align="right" data-id="__react-email-column" style="width:auto;line-height:20px;font-size:14px"><a href="mailto:{{ footer_email_address }}" style="color:rgb(0,0,0);text-decoration:underline;vertical-align:middle" target="_blank"><img src="{{ absolute_url(asset('fa-envelope-black.png', 'email')) }}" alt="" width="16px" height="16px" style="margin-right:8px;vertical-align:middle" />{{ 'email.content_footer.email_label' | trans }}</a></td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding-top:24px;padding-bottom:24px;padding-left:16px;padding-right:16px;max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
              <tbody style="width:100%">
                <tr style="width:100%">
                  <td align="center" data-id="__react-email-column" style="font-size:12px;line-height:16px;vertical-align:middle">{{ 'email.footer.title' | trans }}<img src="{{ theme.footerLogoUrl }}" width="109" height="30" alt="" style="margin-left:16px;vertical-align:middle" /></td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>

</html><!--/$-->
