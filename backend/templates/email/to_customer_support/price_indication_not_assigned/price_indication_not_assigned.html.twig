{% extends 'email/base.html.twig' %}

{% block content %}
    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td>
                <p>{{ 'email.price_indication_not_assigned.salutation' | trans }}</p>
                <p>{{ 'email.price_indication_not_assigned.intro' | trans }}</p>
                {% include 'email/components/button.html.twig' with {'url': url(adminPriceIndicationEditUrl, {'id': id}), 'text': 'email.price_indication_not_assigned.cta' | trans} %}
            </td>
        </tr>
    </table>
{% endblock %}
