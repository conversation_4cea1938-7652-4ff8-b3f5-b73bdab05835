<div>
    <h3>{{ priceCalculationLocality.locality.name }}</h3>

    {% if priceCalculationLocality.surcharges.count > 0 %}
        <div class="alert alert-info" role="alert">
            <h4>Berekende toeslagen:</h4>
            <ul>
                {% for surcharge in priceCalculationLocality.surcharges %}
                    <li>{{ ('surcharge.' ~ surcharge.surchargeType.value)|trans({}, 'admin') }}
                        : {{ surcharge.percentage }}%
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    {% if priceCalculationLocality.priceCalculationLocalityTotals > 0 %}
    <h4>Gevel totalen:</h4>
    <table class="table table-bordered table-striped table-hover sonata-ba-list">
        <tr>
            <th width="25%">Materiaal</th>
            <th width="25%">Totaal excl. toeslag</th>
            <th width="25%">Toeslag percentage</th>
            <th width="25%">Totaal incl. toeslag</th>
        </tr>
        {% for localityTotal in priceCalculationLocality.getPriceCalculationLocalityTotals %}
            <tr>
                <td>{{ localityTotal.material.name }}</td>
                <td>{{ localityTotal.amount }}</td>
                <td>{{ localityTotal.percentage }}%</td>
                <td>{{ localityTotal.total }}</td>
            </tr>
        {% endfor %}
        {% endif %}

        {% for rows in priceCalculationLocality.priceCalculationMaterials %}
        {% set material_id = 0 %}

        {% for material in rows %}
        {% if material.material.id != material_id %}
        {% if material.material.id != 0 %}
    </table>
<br/>
    {% endif %}
    <h5><strong>{{ material.material.name }}:</strong></h5>
    <table class="table table-bordered table-striped table-hover sonata-ba-list">
    <tr>
        <th width="25%">Element</th>
        <th width="25%">Aantal</th>
        <th width="25%">Hoeveelheid</th>
        <th width="25%">Totaal</th>
    </tr>
    {% endif %}
    <tr>
        <td>{{ material.element.name }}</td>
        <td>{{ material.quantity }}</td>
        <td>{{ material.amount }}</td>
        <td>{{ material.total }}</td>
    </tr>
    {% set material_id = material.material.id %}
    {% endfor %}
    </table>
    {% endfor %}
</div>
