<div>
    {% set name = priceCalculationLocalityPlane.specificationLocalityPlane.plane.name %}
    <h3>Vlak: {{ name }}</h3>

    <h4>Details:</h4>
    <div class="box-body table-responsive no-padding">
        <table class="table">
            <tbody>
            <tr class="sonata-ba-view-container">
                <td width="200">Oppervlakte</td>
                <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.squareMeters }} m²</td>
            </tr>
            <tr class="sonata-ba-view-container">
                <td>Ondergrond</td>
                <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.surface.name }}</td>
            </tr>
            <tr class="sonata-ba-view-container">
                <td>Afwerking</td>
                <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.finish.name }}</td>
            </tr>
            <tr class="sonata-ba-view-container">
                <td>Product</td>
                <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.product.name }}</td>
            </tr>
            {% if priceCalculationLocalityPlane.specificationLocalityPlane.innerCorners is not null %}
                <tr class="sonata-ba-view-container">
                    <td>Inwendige hoeken</td>
                    <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.innerCorners }} m</td>
                </tr>
            {% endif %}
            {% if priceCalculationLocalityPlane.specificationLocalityPlane.outerCorners is not null %}
                <tr class="sonata-ba-view-container">
                    <td>Uitwendige hoeken</td>
                    <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.outerCorners }} m</td>
                </tr>
            {% endif %}
            {% if priceCalculationLocalityPlane.specificationLocalityPlane.stopCorners is not null %}
                <tr class="sonata-ba-view-container">
                    <td>Tegen kozijn (stucstop)</td>
                    <td>{{ priceCalculationLocalityPlane.specificationLocalityPlane.stopCorners }} m</td>
                </tr>
            {% endif %}
            </tbody>
        </table>
    </div>

    <br/>

    <h4>Totalen:</h4>
    {% include 'admin/plasterer/specification/_calculation_log_totals_table.html.twig' with {object: priceCalculationLocalityPlane} %}

    <h4>Handelingen:</h4>
    {% for priceCalculationLocalityPlaneHandling in priceCalculationLocalityPlane.priceCalculationLocalityPlaneHandlings %}
        {% include 'admin/plasterer/specification/_calculation_log_locality_plane_handling.html.twig' %}
    {% endfor %}
</div>
