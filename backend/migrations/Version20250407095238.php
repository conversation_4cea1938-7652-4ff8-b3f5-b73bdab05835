<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250407095238 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE plasterer_option_handling DROP FOREIGN KEY FK_AC49B6615AB3F1F');
        $this->addSql('ALTER TABLE plasterer_option_handling DROP FOREIGN KEY FK_AC49B661A7C41D6F');
        $this->addSql('ALTER TABLE plasterer_option DROP FOREIGN KEY FK_10D1D54188823A92');
        $this->addSql('ALTER TABLE plasterer_option DROP FOREIGN KEY FK_10D1D5412B4667EB');
        $this->addSql('ALTER TABLE plasterer_option DROP FOREIGN KEY FK_10D1D541CA11F534');
        $this->addSql('ALTER TABLE plasterer_option DROP FOREIGN KEY FK_10D1D5414584665A');
        $this->addSql('ALTER TABLE plasterer_option DROP FOREIGN KEY FK_10D1D541F53666A8');
        $this->addSql('DROP TABLE plasterer_option_handling');
        $this->addSql('DROP TABLE plasterer_option');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE plasterer_option_handling (option_id INT UNSIGNED NOT NULL, handling_id INT UNSIGNED NOT NULL, INDEX IDX_AC49B661A7C41D6F (option_id), INDEX IDX_AC49B6615AB3F1F (handling_id), PRIMARY KEY(option_id, handling_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE plasterer_option (id INT UNSIGNED AUTO_INCREMENT NOT NULL, locality_id INT UNSIGNED NOT NULL, plane_id INT UNSIGNED NOT NULL, surface_id INT UNSIGNED NOT NULL, finish_id INT UNSIGNED NOT NULL, product_id INT UNSIGNED NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_10D1D54188823A92 (locality_id), INDEX IDX_10D1D5414584665A (product_id), INDEX IDX_10D1D541F53666A8 (plane_id), INDEX IDX_10D1D541CA11F534 (surface_id), INDEX IDX_10D1D5412B4667EB (finish_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE plasterer_option_handling ADD CONSTRAINT FK_AC49B6615AB3F1F FOREIGN KEY (handling_id) REFERENCES plasterer_handling (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option_handling ADD CONSTRAINT FK_AC49B661A7C41D6F FOREIGN KEY (option_id) REFERENCES plasterer_option (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option ADD CONSTRAINT FK_10D1D54188823A92 FOREIGN KEY (locality_id) REFERENCES plasterer_locality (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option ADD CONSTRAINT FK_10D1D5412B4667EB FOREIGN KEY (finish_id) REFERENCES plasterer_finish (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option ADD CONSTRAINT FK_10D1D541CA11F534 FOREIGN KEY (surface_id) REFERENCES plasterer_surface (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option ADD CONSTRAINT FK_10D1D5414584665A FOREIGN KEY (product_id) REFERENCES plasterer_product (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_option ADD CONSTRAINT FK_10D1D541F53666A8 FOREIGN KEY (plane_id) REFERENCES plasterer_plane (id) ON DELETE CASCADE');
    }
}
