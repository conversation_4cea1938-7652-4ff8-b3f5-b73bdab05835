<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250512182521 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE plasterer_specification_handling_material_amount (specification_id CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', handling_id INT UNSIGNED NOT NULL, material_id INT UNSIGNED NOT NULL, amount DOUBLE PRECISION UNSIGNED NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_2F2D09C0908E2FFE (specification_id), INDEX IDX_2F2D09C05AB3F1F (handling_id), INDEX IDX_2F2D09C0E308AC6F (material_id), PRIMARY KEY(specification_id, handling_id, material_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount ADD CONSTRAINT FK_2F2D09C0908E2FFE FOREIGN KEY (specification_id) REFERENCES plasterer_specification (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount ADD CONSTRAINT FK_2F2D09C05AB3F1F FOREIGN KEY (handling_id) REFERENCES plasterer_handling (id)');
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount ADD CONSTRAINT FK_2F2D09C0E308AC6F FOREIGN KEY (material_id) REFERENCES plasterer_material (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount DROP FOREIGN KEY FK_2F2D09C0908E2FFE');
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount DROP FOREIGN KEY FK_2F2D09C05AB3F1F');
        $this->addSql('ALTER TABLE plasterer_specification_handling_material_amount DROP FOREIGN KEY FK_2F2D09C0E308AC6F');
        $this->addSql('DROP TABLE plasterer_specification_handling_material_amount');
    }
}
