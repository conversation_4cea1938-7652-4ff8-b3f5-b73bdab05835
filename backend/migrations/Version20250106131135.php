<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250106131135 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX UNIQ_474A8DB996901F54 ON quotation');
        $this->addSql('ALTER TABLE `quotation` MODIFY COLUMN `number` VARCHAR(255)  CHARACTER SET utf8mb4  COLLATE utf8mb4_unicode_ci  NOT NULL AFTER `id`;');

        $contractors = $this->connection->executeQuery('SELECT id FROM contractor')->fetchFirstColumn();

        foreach ($contractors as $contractor) {
            $quotations = $this->connection->executeQuery('SELECT id FROM quotation WHERE contractor_id = ? ORDER BY created_at', [$contractor])->fetchFirstColumn();

            foreach ($quotations as $key => $quotation) {
                $this->addSql('UPDATE quotation SET number = ? WHERE id = ?', [$this->generateNumber($key + 1), $quotation]);
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE UNIQUE INDEX UNIQ_474A8DB996901F54 ON quotation (number)');
    }

    private function generateNumber(int $number): string
    {
        $formattedNumber = str_pad((string) $number, 4, '0', STR_PAD_LEFT);

        return sprintf('%s-%s', 'OF', $formattedNumber);
    }
}
