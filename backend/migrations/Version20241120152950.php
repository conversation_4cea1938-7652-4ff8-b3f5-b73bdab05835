<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241120152950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("UPDATE material_unit SET name = 'stuks', short_name = 'x' WHERE id = 1;");
        $this->addSql("UPDATE material_unit SET name = 'liter', short_name = 'l' WHERE id = 2;");
        $this->addSql("UPDATE material_unit SET name = 'strekkende meter', short_name = 'm' WHERE id = 3;");
        $this->addSql("UPDATE material_unit SET name = 'uur', short_name = 'u' WHERE id = 4;");
        $this->addSql("UPDATE material_unit SET name = 'stuks', short_name = 'x' WHERE id = 5;");
        $this->addSql("UPDATE material_unit SET name = 'vierkante meter', short_name = 'm²' WHERE id = 6;");
        $this->addSql("UPDATE material_unit SET name = 'kilogram', short_name = 'kg' WHERE id = 7;");
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }
}
