<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241210102434 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE price_indication_photo (id CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', file_id CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', price_indication_locality_id CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\', description VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', UNIQUE INDEX UNIQ_745BF15493CB796C (file_id), INDEX IDX_745BF1541554AA9A (price_indication_locality_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE price_indication_photo ADD CONSTRAINT FK_745BF15493CB796C FOREIGN KEY (file_id) REFERENCES file (id)');
        $this->addSql('ALTER TABLE price_indication_photo ADD CONSTRAINT FK_745BF1541554AA9A FOREIGN KEY (price_indication_locality_id) REFERENCES price_indication_locality (id)');
        $this->addSql('ALTER TABLE price_indication CHANGE comments description VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE price_indication_photo DROP FOREIGN KEY FK_745BF15493CB796C');
        $this->addSql('ALTER TABLE price_indication_photo DROP FOREIGN KEY FK_745BF1541554AA9A');
        $this->addSql('DROP TABLE price_indication_photo');
        $this->addSql('ALTER TABLE price_indication CHANGE description comments VARCHAR(255) DEFAULT NULL');
    }
}
