<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241118093159 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        foreach ($this->getRows() as $row) {
            $this->addSql('INSERT INTO material_price (material_id, rounding_nearest, minimal_quantity, purchase_price, sale_price, created_at, updated_at) VALUES (:id, :rounding_nearest, :minimal_quantity, :purchase_price, :sale_price, NOW(), NOW())', $row);
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }

    public function getRows()
    {
        return [
            0 => [
                'id' => '1',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '38',
                'sale_price' => '53',
            ],
            1 => [
                'id' => '2',
                'rounding_nearest' => '0.29',
                'minimal_quantity' => '0.29',
                'purchase_price' => '4',
                'sale_price' => '4.5',
            ],
            2 => [
                'id' => '3',
                'rounding_nearest' => '0.3',
                'minimal_quantity' => '0',
                'purchase_price' => '78',
                'sale_price' => '89',
            ],
            3 => [
                'id' => '4',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '76',
                'sale_price' => '87',
            ],
            4 => [
                'id' => '5',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '48',
                'sale_price' => '52',
            ],
            5 => [
                'id' => '6',
                'rounding_nearest' => '5',
                'minimal_quantity' => '5',
                'purchase_price' => '8',
                'sale_price' => '12',
            ],
            6 => [
                'id' => '7',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '8',
                'sale_price' => '10',
            ],
            7 => [
                'id' => '8',
                'rounding_nearest' => '0',
                'minimal_quantity' => '0',
                'purchase_price' => '25',
                'sale_price' => '31',
            ],
            8 => [
                'id' => '9',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '38',
                'sale_price' => '53',
            ],
            9 => [
                'id' => '10',
                'rounding_nearest' => '1',
                'minimal_quantity' => '1',
                'purchase_price' => '38',
                'sale_price' => '45',
            ],
            10 => [
                'id' => '11',
                'rounding_nearest' => '1',
                'minimal_quantity' => '25',
                'purchase_price' => '3.5',
                'sale_price' => '4',
            ],
        ];
    }
}
