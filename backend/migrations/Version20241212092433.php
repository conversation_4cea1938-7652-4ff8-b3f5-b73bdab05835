<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241212092433 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE specification_photo DROP INDEX UNIQ_AAF4177F93CB796C, ADD INDEX IDX_AAF4177F93CB796C (file_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE specification_photo DROP INDEX IDX_AAF4177F93CB796C, ADD UNIQUE INDEX UNIQ_AAF4177F93CB796C (file_id)');
    }
}
