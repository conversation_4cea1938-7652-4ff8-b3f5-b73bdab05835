<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250227163357 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quotation ADD accepted_by_id CHAR(36) DEFAULT NULL COMMENT \'(DC2Type:uuid)\' AFTER quotation_accepted_by_id, ADD cancelled_by_id CHAR(36) DEFAULT NULL COMMENT \'(DC2Type:uuid)\' AFTER quotation_cancelled_by_id, CHANGE quotation_accepted_at accepted_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE quotation ADD CONSTRAINT FK_474A8DB920F699D9 FOREIGN KEY (accepted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE quotation ADD CONSTRAINT FK_474A8DB9187B2D12 FOREIGN KEY (cancelled_by_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_474A8DB920F699D9 ON quotation (accepted_by_id)');
        $this->addSql('CREATE INDEX IDX_474A8DB9187B2D12 ON quotation (cancelled_by_id)');
        $this->addSql('UPDATE quotation SET accepted_by_id = quotation_accepted_by_id');
        $this->addSql('UPDATE quotation SET cancelled_by_id = quotation_cancelled_by_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quotation DROP FOREIGN KEY FK_474A8DB920F699D9');
        $this->addSql('ALTER TABLE quotation DROP FOREIGN KEY FK_474A8DB9187B2D12');
        $this->addSql('DROP INDEX IDX_474A8DB920F699D9 ON quotation');
        $this->addSql('DROP INDEX IDX_474A8DB9187B2D12 ON quotation');
        $this->addSql('ALTER TABLE quotation DROP accepted_by_id, DROP cancelled_by_id, CHANGE accepted_at quotation_accepted_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }
}
