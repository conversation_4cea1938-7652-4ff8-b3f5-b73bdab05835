<?php

declare(strict_types=1);

namespace App\Domain\Model\Material;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use App\Domain\Assert;
use App\Domain\Model\LegacyTrait;
use App\Domain\Model\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ApiResource(
    operations: [
        new Get(),
        new GetCollection(),
    ],
    normalizationContext: ['groups' => ['material_unit:read']],
)]
#[ORM\Entity]
class MaterialUnit
{
    use LegacyTrait;
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    #[Groups(['material_unit:read'])]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    #[Groups(['material_unit:read'])]
    private string $name;

    #[ORM\Column(type: Types::STRING)]
    #[Groups(['material_unit:read'])]
    private string $shortName;

    public function __construct(
        string $name,
        string $shortName,
    ) {
        Assert::stringNotEmpty($name);
        Assert::stringNotEmpty($shortName);

        $this->name = $name;
        $this->shortName = $shortName;
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): int
    {
        Assert::integer($this->id);

        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getShortName(): string
    {
        return $this->shortName;
    }
}
