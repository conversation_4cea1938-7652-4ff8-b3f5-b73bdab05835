<?php

declare(strict_types=1);

namespace App\Domain\Model\Maintenance;

use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\GetCollection;
use App\Domain\Model\TimestampableEntity;
use App\Domain\Model\User\Roles;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ApiResource(
    operations: [
        new GetCollection(
            security: 'is_granted("'.Roles::ROLE_CONTRACTOR.'")'
        ),
    ],
    normalizationContext: ['groups' => ['maintenance_job_type:read']],
)]
#[ApiFilter(SearchFilter::class, properties: [
    'maintenancePlan.identifier' => 'exact',
])]
#[ORM\Entity]
class MaintenanceJobType
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    #[Groups(['maintenance_job_type:read'])]
    private string $name;

    #[ORM\Column(type: Types::TEXT, length: 1000)]
    #[Groups(['maintenance_job_type:read'])]
    private string $description;

    #[ORM\ManyToOne(targetEntity: MaintenancePlan::class)]
    #[ORM\JoinColumn(nullable: false)]
    private MaintenancePlan $maintenancePlan;

    public function __construct(string $name, string $description, MaintenancePlan $maintenancePlan)
    {
        $this->name = $name;
        $this->description = $description;
        $this->maintenancePlan = $maintenancePlan;
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(string $name, string $description): void
    {
        $this->name = $name;
        $this->description = $description;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getMaintenancePlan(): MaintenancePlan
    {
        return $this->maintenancePlan;
    }

    public function setMaintenancePlan(MaintenancePlan $maintenancePlan): void
    {
        $this->maintenancePlan = $maintenancePlan;
    }

    public function __toString(): string
    {
        return $this->name ?? '';
    }
}
