<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Put;
use App\Application\Security\Voter\AccessType;
use App\Domain\MessageRecordingInterface;
use App\Domain\MessageRecordingTrait;
use App\Domain\Model\Element\Element;
use App\Domain\Model\ElementMaterialAmount\ElementMaterialAmount;
use App\Domain\Model\JobType\JobType;
use App\Domain\Model\Material\Material;
use App\Domain\Model\Material\MaterialPrice;
use App\Domain\Model\Painter\Quotation\Quotation;
use App\Domain\Model\PriceIndication\PriceIndication;
use App\Domain\Model\Specification\Calculator\SpecificationPriceCalculator\PriceCalculation;
use App\Domain\Model\Specification\Command\CalculateSpecificationCommand;
use App\Domain\Model\Specification\Command\UpdateSpecificationCommand;
use App\Domain\Model\System\System;
use App\Domain\Model\TimestampableEntity;
use App\Domain\Model\Vat\VatPercentage;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use DomainException;
use Money\Money;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Serializer\Annotation\Groups;

#[ApiResource(
    operations: [
        new Get(
            security: 'is_granted("'.AccessType::READ.'", object)',
        ),
        new Put(
            security: 'is_granted("'.AccessType::UPDATE.'", object)',
            input: UpdateSpecificationCommand::class,
            output: Specification::class,
            messenger: 'input',
        ),
    ],
    normalizationContext: ['groups' => ['specification:read']],
)]
#[ORM\Entity]
class Specification implements MessageRecordingInterface
{
    use MessageRecordingTrait;
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: 'uuid')]
    #[Groups(['specification:read'])]
    private UuidInterface $id;

    #[ORM\ManyToOne()]
    #[Groups(['specification:read'])]
    #[ORM\JoinColumn(nullable: false)]
    private JobType $jobType;

    /**
     * @var Collection<int, SpecificationLocality>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationLocality::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[Groups(['specification:read'])]
    #[ApiProperty(readableLink: true)]
    #[ORM\OrderBy(['createdAt' => 'ASC'])]
    private Collection $specificationLocalities;

    /**
     * @var Collection<int, SpecificationExtra>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationExtra::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[Groups(['specification:read'])]
    #[ApiProperty(readableLink: true)]
    private Collection $specificationExtras;

    /**
     * @var Collection<int, SpecificationMaterialPrice>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationMaterialPrice::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[ApiProperty(readableLink: true)]
    private Collection $specificationMaterialPrices;

    /**
     * @var Collection<int, SpecificationMaterialTotal>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationMaterialTotal::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[Groups(['specification:read'])]
    private Collection $specificationMaterialTotals;

    /**
     * @var Collection<int, SpecificationElementMaterialAmount>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationElementMaterialAmount::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $specificationElementMaterialAmounts;

    /**
     * @var Collection<int, SpecificationVatPrice>
     */
    #[ORM\OneToMany(mappedBy: 'specification', targetEntity: SpecificationVatPrice::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[Groups(['specification:read'])]
    private Collection $specificationVatPrices;

    #[ORM\OneToOne(mappedBy: 'specification', targetEntity: PriceIndication::class)]
    private ?PriceIndication $priceIndication = null;

    #[ORM\OneToOne(mappedBy: 'specification', targetEntity: Quotation::class)]
    private ?Quotation $quotation = null;

    #[ORM\OneToOne(mappedBy: 'specification', targetEntity: SpecificationMaintenancePlan::class, cascade: ['persist', 'remove'], orphanRemoval: true)]
    #[Groups(['specification:read'])]
    private ?SpecificationMaintenancePlan $specificationMaintenancePlan = null;

    #[ORM\Column(type: Types::INTEGER, enumType: VatPercentage::class)]
    #[Groups(['specification:read'])]
    private VatPercentage $vatPercentage;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    #[Groups(['specification:read'])]
    private ?float $calculatedHours = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    #[Groups(['specification:read'])]
    private ?float $hours = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $hoursPrice = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $calculatedMaterialPrice = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $materialPrice = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    #[Groups(['specification:read'])]
    private ?int $discountPercentage = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $discountPrice = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(['specification:read'])]
    private ?string $quotationDescription = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(['specification:read'])]
    private ?string $quotationExclusion = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $priceExclVat = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $priceInclVat = null;

    #[ORM\Column(type: 'money', nullable: true)]
    #[Groups(['specification:read'])]
    private ?Money $inputPrice = null;

    /**
     * @param array<SpecificationLocality> $specificationLocalities
     */
    public function __construct(array $specificationLocalities, JobType $jobType, VatPercentage $vatPercentage = VatPercentage::VatPercentage9)
    {
        $this->id = Uuid::uuid4();
        $this->vatPercentage = $vatPercentage;
        $this->specificationLocalities = new ArrayCollection();
        $this->specificationExtras = new ArrayCollection();
        $this->specificationMaterialPrices = new ArrayCollection();
        $this->specificationElementMaterialAmounts = new ArrayCollection();
        $this->specificationMaterialTotals = new ArrayCollection();
        $this->specificationVatPrices = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
        $this->addSpecifications($specificationLocalities);
        $this->jobType = $jobType;
    }

    public function update(
        VatPercentage $vatPercentage,
        ?float $hours,
        ?Money $materialPrice,
        ?int $discountPercentage,
        ?Money $inputPrice,
        ?string $quotationDescription,
        ?string $quotationExclusion,
    ): void {
        $this->vatPercentage = $vatPercentage;
        $this->hours = $hours;
        $this->materialPrice = $materialPrice;
        $this->discountPercentage = $discountPercentage;
        $this->inputPrice = $inputPrice;
        $this->quotationDescription = $quotationDescription;
        $this->quotationExclusion = $quotationExclusion;
        $this->updatedAt = new DateTimeImmutable();

        $this->recordMessage(new CalculateSpecificationCommand($this->getId()));
    }

    /**
     * @param array<SpecificationLocality> $specificationLocalities
     */
    private function addSpecifications(array $specificationLocalities): void
    {
        foreach ($specificationLocalities as $specificationLocality) {
            $this->addSpecification($specificationLocality);
        }
    }

    public function addSpecification(SpecificationLocality $specificationLocality): void
    {
        if (!$this->specificationLocalities->contains($specificationLocality)) {
            $specificationLocality->setSpecification($this);
            $this->specificationLocalities->add($specificationLocality);
        }
    }

    /**
     * @param array<int, MaterialPrice> $materialPrices
     */
    public function addSpecificationMaterialPrices(array $materialPrices): void
    {
        $mapping = $this->getSpecificationMaterialPricesMapping();

        foreach ($materialPrices as $materialPrice) {
            if (isset($mapping[$materialPrice->getMaterial()->getId()])) {
                continue;
            }

            $specificationMaterialPrice = new SpecificationMaterialPrice(
                $this,
                $materialPrice->getMaterial(),
                $materialPrice->getPurchasePrice(),
                $materialPrice->getSalePrice(),
                $materialPrice->getRoundingNearest(),
                $materialPrice->getMinimalQuantity()
            );

            $this->addSpecificationMaterialPrice($specificationMaterialPrice);
            $mapping[$materialPrice->getMaterial()->getId()] = $specificationMaterialPrice;
        }
    }

    public function addSpecificationMaterialPrice(SpecificationMaterialPrice $specificationMaterialPrice): void
    {
        if (!$this->specificationMaterialPrices->contains($specificationMaterialPrice)) {
            $this->specificationMaterialPrices->add($specificationMaterialPrice);
        }
    }

    /**
     * @return array<int, SpecificationMaterialPrice>
     */
    public function getSpecificationMaterialPricesMapping(): array
    {
        $mapping = [];

        foreach ($this->specificationMaterialPrices as $specificationMaterialPrice) {
            $mapping[(int) $specificationMaterialPrice->getMaterial()->getId()] = $specificationMaterialPrice;
        }

        return $mapping;
    }

    /**
     * @param array<int, ElementMaterialAmount> $elementMaterialAmounts
     */
    public function addSpecificationMaterialAmounts(array $elementMaterialAmounts): void
    {
        $mapping = $this->getSpecificationElementMaterialAmountMapping();

        foreach ($elementMaterialAmounts as $elementMaterialAmount) {
            if ($elementMaterialAmount->getAmount() <= 0) {
                continue;
            }

            $elementId = $elementMaterialAmount->getElement()->getId();
            $systemId = $elementMaterialAmount->getSystem()->getId();
            $materialId = $elementMaterialAmount->getMaterial()->getId();

            if (isset($mapping[$elementId][$materialId][$systemId])) {
                continue;
            }

            $specificationElementMaterialAmount = new SpecificationElementMaterialAmount(
                $this,
                $elementMaterialAmount->getElement(),
                $elementMaterialAmount->getMaterial(),
                $elementMaterialAmount->getSystem(),
                $elementMaterialAmount->getAmount()
            );

            $this->addSpecificationMaterialAmount($specificationElementMaterialAmount);
            $mapping[$elementId][$materialId][$systemId] = $specificationElementMaterialAmount;
        }
    }

    private function addSpecificationMaterialAmount(SpecificationElementMaterialAmount $specificationElementMaterialAmount): void
    {
        if (!$this->specificationElementMaterialAmounts->contains($specificationElementMaterialAmount)) {
            $this->specificationElementMaterialAmounts->add($specificationElementMaterialAmount);
        }
    }

    /**
     * @return Collection<int, SpecificationElementMaterialAmount>
     */
    public function getSpecificationElementMaterialAmounts(): Collection
    {
        return $this->specificationElementMaterialAmounts;
    }

    /**
     * @return Collection<int, SpecificationElementMaterialAmount>
     */
    public function getSpecificationElementMaterialAmountsByElementAndSystem(Element $element, System $system): Collection
    {
        $expressionBuilder = Criteria::expr();

        $expression = $expressionBuilder->andX(
            $expressionBuilder->eq('element', $element),
            $expressionBuilder->eq('system', $system)
        );

        return $this->specificationElementMaterialAmounts->matching(new Criteria($expression));
    }

    /**
     * @return array<int, array<int, array<int, SpecificationElementMaterialAmount>>>
     */
    private function getSpecificationElementMaterialAmountMapping(): array
    {
        $mapping = [];

        foreach ($this->specificationElementMaterialAmounts as $specificationElementMaterialAmount) {
            $mapping[$specificationElementMaterialAmount->getElement()->getId()][$specificationElementMaterialAmount->getMaterial()->getId()][$specificationElementMaterialAmount->getSystem()->getId()] = $specificationElementMaterialAmount;
        }

        return $mapping;
    }

    /**
     * @return Collection<int, SpecificationMaterialTotal>
     */
    public function getSpecificationMaterialTotals(): Collection
    {
        return $this->specificationMaterialTotals;
    }

    /**
     * @return Collection<int, SpecificationVatPrice>
     */
    public function getSpecificationVatPrices(): Collection
    {
        return $this->specificationVatPrices;
    }

    public function getId(): UuidInterface
    {
        return $this->id;
    }

    /**
     * @return Collection<int, SpecificationLocality>
     */
    public function getSpecificationLocalities(): Collection
    {
        return $this->specificationLocalities;
    }

    /**
     * @return Collection<int, SpecificationExtra>
     */
    public function getSpecificationExtras(): Collection
    {
        return $this->specificationExtras;
    }

    public function getSpecificationMaintenancePlan(): ?SpecificationMaintenancePlan
    {
        return $this->specificationMaintenancePlan;
    }

    public function hasSpecificationMaintenancePlan(): bool
    {
        return $this->specificationMaintenancePlan instanceof SpecificationMaintenancePlan;
    }

    public function setSpecificationMaintenancePlan(?SpecificationMaintenancePlan $specificationMaintenancePlan): void
    {
        $this->specificationMaintenancePlan = $specificationMaintenancePlan;
    }

    public function hasExtras(): bool
    {
        return !$this->specificationExtras->isEmpty();
    }

    public function hasMaintenancePlan(): bool
    {
        return $this->specificationMaintenancePlan instanceof SpecificationMaintenancePlan;
    }

    public function getTotalSpecificationExtraPrice(): Money
    {
        $extraPrice = Money::EUR(0);

        foreach ($this->specificationExtras as $extra) {
            $extraPrice = $extraPrice->add($extra->getPrice());
        }

        return $extraPrice;
    }

    public function getTotalSpecificationVatPrice(): Money
    {
        $vatPrice = Money::EUR(0);

        foreach ($this->specificationVatPrices as $vat) {
            $vatPrice = $vatPrice->add($vat->getPrice());
        }

        return $vatPrice;
    }

    public function getPriceIndication(): ?PriceIndication
    {
        return $this->priceIndication;
    }

    public function getJobType(): JobType
    {
        return $this->jobType;
    }

    public function getOwner(): SpecificationOwnerInterface
    {
        if ($this->priceIndication instanceof PriceIndication) {
            return $this->priceIndication;
        }

        if ($this->quotation instanceof Quotation) {
            return $this->quotation;
        }

        throw new DomainException('Invalid or no owner found');
    }

    public function setOwner(SpecificationOwnerInterface $owner): void
    {
        if ($owner instanceof PriceIndication) {
            $this->priceIndication = $owner;

            return;
        }

        if ($owner instanceof Quotation) {
            $this->quotation = $owner;

            return;
        }

        throw new DomainException('Invalid or no owner found');
    }

    public function canBeUpdated(): bool
    {
        return $this->getOwner()->canUpdateSpecification();
    }

    public function getConstructionYear(): string
    {
        if (!$this->priceIndication instanceof PriceIndication) {
            return '';
        }

        return (string) $this->priceIndication->getConstructionYear();
    }

    public function getVatPercentage(): VatPercentage
    {
        return $this->vatPercentage;
    }

    public function getCalculatedHours(): ?float
    {
        return $this->calculatedHours;
    }

    public function getHours(): ?float
    {
        return $this->hours;
    }

    public function getHoursPrice(): ?Money
    {
        return $this->hoursPrice;
    }

    public function getCalculatedMaterialPrice(): ?Money
    {
        return $this->calculatedMaterialPrice;
    }

    public function getMaterialPrice(): ?Money
    {
        return $this->materialPrice;
    }

    public function getMaterialPlusHoursPrice(): ?Money
    {
        $materialPrice = $this->getMaterialPrice() ?? $this->getCalculatedMaterialPrice();
        $hoursPrice = $this->getHoursPrice();

        if (!$materialPrice instanceof Money && !$hoursPrice instanceof Money) {
            return null;
        }

        if (!$materialPrice instanceof Money) {
            return $hoursPrice;
        }

        if (!$hoursPrice instanceof Money) {
            return $materialPrice;
        }

        return $materialPrice->add($hoursPrice);
    }

    public function getDiscountPercentage(): ?int
    {
        return $this->discountPercentage;
    }

    public function getDiscountPrice(): ?Money
    {
        return $this->discountPrice;
    }

    public function getQuotationDescription(): ?string
    {
        return $this->quotationDescription;
    }

    public function getQuotationExclusion(): ?string
    {
        return $this->quotationExclusion;
    }

    public function getPriceExclVat(): ?Money
    {
        return $this->priceExclVat;
    }

    public function getPriceInclVat(): ?Money
    {
        return $this->priceInclVat;
    }

    public function getPriceExclVatWithoutMaintenanceExcludedExtras(): ?Money
    {
        if (!$this->getPriceExclVat() instanceof Money) {
            return null;
        }

        $excludedExtrasPrice = Money::EUR(0);
        foreach ($this->specificationExtras as $extra) {
            if ($extra->isProvisional() || !$extra->isIncludeInMaintenance()) {
                $excludedExtrasPrice = $excludedExtrasPrice->add($extra->getPrice());
            }
        }

        return $this->getPriceExclVat()->subtract($excludedExtrasPrice);
    }

    public function updatePrices(PriceCalculation $priceCalculation): void
    {
        $this->calculatedHours = $priceCalculation->getCalculatedHours();
        $this->calculatedMaterialPrice = $priceCalculation->getCalculatedMaterialPrice();
        $this->hoursPrice = $priceCalculation->getHoursPrice();
        $this->discountPrice = $priceCalculation->getDiscountPrice();
        $this->priceExclVat = $priceCalculation->getPriceExclVat();
        $this->priceInclVat = $priceCalculation->getPriceInclVat();

        $this->specificationMaterialTotals->clear();
        foreach ($priceCalculation->getPriceCalculationTotals() as $priceCalculationMaterialTotal) {
            $this->addSpecificationMaterialTotal($priceCalculationMaterialTotal->getMaterial(), $priceCalculationMaterialTotal->getRoundedAmount());
        }

        $this->specificationVatPrices->clear();
        foreach ($priceCalculation->getVatPrices() as $percentage => $specificationVatPrice) {
            $this->addSpecificationVatPrice(VatPercentage::from($percentage), $specificationVatPrice);
        }

        if (!$this->getPriceInclVat() instanceof Money) {
            throw new DomainException('Price is not calculated');
        }

        $this->getOwner()->updatePrice($this->getPriceInclVat());
    }

    private function addSpecificationMaterialTotal(Material $material, float $amount): void
    {
        $specificationMaterialTotal = new SpecificationMaterialTotal($this, $material, $amount);

        if (!$this->specificationMaterialTotals->contains($specificationMaterialTotal)) {
            $this->specificationMaterialTotals->add($specificationMaterialTotal);
        }
    }

    private function addSpecificationVatPrice(VatPercentage $getVatPercentage, Money $vatPrice): void
    {
        $specificationVatPrice = new SpecificationVatPrice($this, $getVatPercentage, $vatPrice);

        if (!$this->specificationVatPrices->contains($specificationVatPrice)) {
            $this->specificationVatPrices->add($specificationVatPrice);
        }
    }

    public function clone(?VatPercentage $vatPercentage = null): self
    {
        $clone = clone $this;

        if ($vatPercentage instanceof VatPercentage) {
            $clone->vatPercentage = $vatPercentage;
        }

        return $clone;
    }

    public function __clone(): void
    {
        $this->id = Uuid::uuid4();
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = null;
        $this->priceIndication = null;
        $this->quotation = null;

        $clonedSpecificationLocalities = array_map(fn (SpecificationLocality $locality) => $locality->clone($this), $this->specificationLocalities->toArray());
        $this->specificationLocalities = new ArrayCollection($clonedSpecificationLocalities);

        $clonedSpecificationExtras = array_map(fn (SpecificationExtra $extra) => $extra->clone($this), $this->specificationExtras->toArray());
        $this->specificationExtras = new ArrayCollection($clonedSpecificationExtras);

        $this->specificationMaintenancePlan = $this->specificationMaintenancePlan?->clone($this);

        // clear price calculation fields
        $this->hoursPrice = null;
        $this->calculatedHours = null;
        $this->calculatedMaterialPrice = null;
        $this->priceInclVat = null;
        $this->priceExclVat = null;
        $this->discountPrice = null;

        // clear other collections
        $this->specificationMaterialPrices = new ArrayCollection();
        $this->specificationElementMaterialAmounts = new ArrayCollection();
        $this->specificationMaterialTotals = new ArrayCollection();
        $this->specificationVatPrices = new ArrayCollection();

        // recalculate
        $this->recordMessage(new CalculateSpecificationCommand($this->id));
    }

    public function getInputPrice(): ?Money
    {
        return $this->inputPrice;
    }
}
