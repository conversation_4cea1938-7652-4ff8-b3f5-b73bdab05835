<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Controller;

use App\Application\Security\Voter\AccessType;
use App\Domain\Model\Specification\Command\DeleteSpecificationMaintenanceJobCommand;
use App\Domain\Model\Specification\SpecificationMaintenanceJob;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

final readonly class DeleteSpecificationMaintenanceJobController
{
    public function __construct(private Security $security)
    {
    }

    public function __invoke(SpecificationMaintenanceJob $specificationMaintenanceJob): DeleteSpecificationMaintenanceJobCommand
    {
        if (!$this->security->isGranted(AccessType::UPDATE, $specificationMaintenanceJob->getSpecification())) {
            throw new AccessDeniedHttpException();
        }

        return new DeleteSpecificationMaintenanceJobCommand($specificationMaintenanceJob->getId());
    }
}
