<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Command;

use App\Domain\Attribute\MapParameter;
use Ramsey\Uuid\UuidInterface;

final readonly class UpdateSpecificationMaintenancePlanCommand
{
    public function __construct(
        #[MapParameter(key: 'id')]
        public UuidInterface $specificationMaintenancePlan,
        public float $indexationPercentage,
    ) {
    }
}
