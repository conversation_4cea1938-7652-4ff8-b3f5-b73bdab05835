<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Command;

use App\Domain\Attribute\MapParameter;
use App\Domain\Model\Vat\VatPercentage;
use Money\Money;
use Ramsey\Uuid\UuidInterface;

final class UpdateSpecificationCommand
{
    public function __construct(
        #[MapParameter(key: 'id')]
        public UuidInterface $specificationId,
        public VatPercentage $vatPercentage,
        public ?float $hours,
        public ?Money $materialPrice,
        public ?Money $inputPrice,
        public ?int $discountPercentage,
        public ?string $quotationDescription,
        public ?string $quotationExclusion,
    ) {
        if ($this->inputPrice !== null && $this->discountPercentage !== null) {
            throw new \InvalidArgumentException('Cannot provide both inputPrice and discountPercentage at the same time.');
        }
    }
}
