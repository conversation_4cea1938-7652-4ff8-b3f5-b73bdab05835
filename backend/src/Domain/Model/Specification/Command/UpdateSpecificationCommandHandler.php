<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Command;

use App\Domain\Exception\DomainException;
use App\Domain\Model\Specification\Specification;
use App\Domain\RepositoryInterface;
use App\Domain\Resolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateSpecificationCommandHandler
{
    public function __construct(
        private RepositoryInterface $repository,
        private Resolver $resolver,
    ) {
    }

    public function __invoke(UpdateSpecificationCommand $command): Specification
    {
        $specification = $this->resolver->resolve(Specification::class, $command->specificationId);

        if (!$specification->canBeUpdated()) {
            throw new DomainException('Specification cannot be updated');
        }

        $specification->update(
            $command->vatPercentage,
            $command->hours,
            $command->materialPrice,
            $command->discountPercentage,
            $command->inputPrice,
            $command->quotationDescription,
            $command->quotationExclusion,
        );

        $this->repository->save();

        return $specification;
    }
}
