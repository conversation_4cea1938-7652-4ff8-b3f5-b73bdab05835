<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Calculator\SpecificationPriceCalculator;

use App\Domain\Model\Material\Material;

final class PriceCalculationLocalityTotal
{
    private int $percentage = 0;

    public function __construct(private readonly Material $material, private float $amount)
    {
    }

    public function addAmount(float $amount): void
    {
        $this->amount += $amount;
    }

    public function addPercentage(int $percentage): void
    {
        $this->percentage += $percentage;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getTotal(): float
    {
        return $this->amount * (1 + $this->percentage / 100);
    }

    public function getPercentage(): int
    {
        return $this->percentage;
    }

    public function getMaterial(): Material
    {
        return $this->material;
    }
}
