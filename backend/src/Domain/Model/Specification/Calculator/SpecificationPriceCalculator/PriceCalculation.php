<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Calculator\SpecificationPriceCalculator;

use App\Domain\Model\Specification\Specification;
use App\Domain\Model\Specification\SpecificationMaterialPrice;
use App\Domain\Model\System\System;
use Money\Money;

final class PriceCalculation
{
    /** @var array<PriceCalculationLocality> */
    private array $priceCalculationLocalities = [];

    /** @var array<PriceCalculationTotal> */
    private array $priceCalculationTotals = [];

    /** @var array<SpecificationMaterialPrice> */
    private array $materialPrices;

    public function __construct(private Specification $specification)
    {
        $this->materialPrices = $specification->getSpecificationMaterialPricesMapping();

        foreach ($specification->getSpecificationLocalities() as $specificationLocality) {
            $priceCalculationLocality = new PriceCalculationLocality($specificationLocality);
            if (!$specificationLocality->getSystem() instanceof System) {
                continue;
            }

            foreach ($specificationLocality->getSpecificationElements() as $element) {
                $materialAmounts = $specification->getSpecificationElementMaterialAmountsByElementAndSystem($element->getElement(), $specificationLocality->getSystem());

                foreach ($materialAmounts as $materialAmount) {
                    $priceCalculationMaterial = new PriceCalculationMaterial($element->getElement(), $materialAmount->getMaterial(), $element->getQuantity(), $materialAmount->getAmount());

                    $priceCalculationLocality->addPriceCalculationMaterial($priceCalculationMaterial);
                }
            }

            $this->addPriceCalculationLocality($priceCalculationLocality);
        }
    }

    public function addPriceCalculationLocality(PriceCalculationLocality $priceCalculationLocality): void
    {
        $this->priceCalculationLocalities[] = $priceCalculationLocality;

        foreach ($priceCalculationLocality->getPriceCalculationLocalityTotals() as $materialId => $amount) {
            $materialPrices = $this->materialPrices[$materialId];

            if (!isset($this->priceCalculationTotals[$materialId])) {
                $this->priceCalculationTotals[$materialId] = new PriceCalculationTotal($materialPrices, $amount->getTotal());

                continue;
            }

            $this->priceCalculationTotals[$materialId]->addAmount($amount->getTotal());
        }
    }

    /**
     * @return array<PriceCalculationTotal>
     */
    public function getPriceCalculationTotals(): array
    {
        return $this->priceCalculationTotals;
    }

    /**
     * @return array<PriceCalculationLocality>
     */
    public function getPriceCalculationLocality(): array
    {
        return $this->priceCalculationLocalities;
    }

    public function getSpecification(): Specification
    {
        return $this->specification;
    }

    public function getWorkMaterialSalesPrice(): Money
    {
        $workMaterials = array_filter($this->materialPrices, static fn ($materialPrice) => $materialPrice->getMaterial()->isWork());
        $workMaterial = reset($workMaterials);

        return $workMaterial instanceof SpecificationMaterialPrice ? $workMaterial->getSalePrice() : Money::EUR(0);
    }

    public function getCalculatedMaterialPrice(): Money
    {
        $materialTotals = array_filter($this->priceCalculationTotals, static fn ($total) => !$total->isWork());
        $materialPrices = array_map(static fn ($total) => $total->getPrice(), $materialTotals);

        return Money::EUR(0)->add(...$materialPrices);
    }

    public function getCalculatedHours(): float
    {
        $hourTotals = array_filter($this->priceCalculationTotals, static fn ($total) => $total->isWork());
        $roundedAmountArrays = array_map(static fn ($total) => $total->getRoundedAmount(), $hourTotals);

        return array_sum($roundedAmountArrays);
    }

    public function getHoursPrice(): Money
    {
        $totalHours = \is_float($this->specification->getHours()) ? $this->specification->getHours() : $this->getCalculatedHours();

        return $this->getWorkMaterialSalesPrice()->multiply((string) $totalHours);
    }

    public function getMaterialPrice(): Money
    {
        return $this->specification->getMaterialPrice() instanceof Money ? $this->specification->getMaterialPrice() : $this->getCalculatedMaterialPrice();
    }

    public function getDiscountPrice(): ?Money
    {
        /* if input price is set, calculate discount from that */
        if ($this->specification->getInputPrice() instanceof Money) {
            $discountInclVat = $this->getCalculatedPrice()->subtract($this->specification->getInputPrice());
            $discountExclVat = $discountInclVat->divide((string) (1 + ($this->specification->getVatPercentage()->value / 100)));

            return $discountExclVat->multiply(-1);
        }

        $discountPercentage = (int) $this->specification->getDiscountPercentage();
        if (0 === $discountPercentage) {
            return null;
        }

        $totalPrice = $this->getHoursPrice()->add($this->getMaterialPrice())->add($this->specification->getTotalSpecificationExtraPrice());

        return $this->calculateDiscount($totalPrice, $discountPercentage);
    }

    public function getPriceExclVat(bool $includeDiscount = true): Money
    {
        $array = [
            $this->getMaterialPrice(),
            $this->specification->getTotalSpecificationExtraPrice(),
        ];

        if ($includeDiscount) {
            $array[] = $this->getDiscountPrice();
        }

        $exclVatPrices = array_filter($array);

        return $this->getHoursPrice()->add(...$exclVatPrices);
    }

    /**
     * @return Money
     *
     * Get calculated price without discount
     */
    public function getCalculatedPrice(): Money
    {
        return $this->getPriceExclVat(false)->add(...$this->getVatPrices(false));
    }

    /**
     * @return array<int, Money>
     */
    public function getVatPrices(bool $includeDiscount = true): array
    {
        $hoursPrice = $this->getHoursPrice();
        if ($includeDiscount && $this->getDiscountPrice() instanceof Money) {
            $hoursPrice = $hoursPrice->add($this->getDiscountPrice());
        }

        $vatPrices[$this->specification->getVatPercentage()->value] = [
            $hoursPrice,
            $this->getMaterialPrice(),
        ];

        foreach ($this->specification->getSpecificationExtras() as $extra) {
            $vatPrices[$extra->getVatPercentage()->value][] = $extra->getPrice();
        }

        $totalVatPrice = [];
        foreach ($vatPrices as $vatPercentage => $prices) {
            $vatPrice = Money::EUR(0);

            foreach ($prices as $price) {
                if (!$price instanceof Money) {
                    continue;
                }

                $vatPrice = $vatPrice->add($price);
            }

            $totalVatPrice[$vatPercentage] = $this->calculateVat($vatPrice, $vatPercentage);
        }

        return $totalVatPrice;
    }

    public function getPriceInclVat(): Money
    {
        return $this->getPriceExclVat()->add(...$this->getVatPrices());
    }

    private function calculateVat(Money $price, int $vatPercentage): Money
    {
        $vat = ($price->getAmount() / 10000) * $vatPercentage;

        return Money::EUR((string) round($vat * 100));
    }

    private function calculateDiscount(Money $price, int $discountPercentage): Money
    {
        $discount = ($price->getAmount() / 10000) * $discountPercentage;

        return Money::EUR((string) round($discount * 100))->multiply(-1);
    }
}
