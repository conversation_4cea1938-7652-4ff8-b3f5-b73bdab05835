<?php

declare(strict_types=1);

namespace App\Domain\Model\Specification\Calculator\SpecificationPriceCalculator;

use App\Domain\Model\Specification\SpecificationLocalitySurchargeType;

final readonly class PriceCalculationSurcharge
{
    public function __construct(
        private SpecificationLocalitySurchargeType $type,
        private int $percentage,
        private bool $onWork,
        private bool $onMaterial,
    ) {
    }

    public function getType(): SpecificationLocalitySurchargeType
    {
        return $this->type;
    }

    public function getPercentage(): int
    {
        return $this->percentage;
    }

    public function getOnWork(): bool
    {
        return $this->onWork;
    }

    public function getOnMaterial(): bool
    {
        return $this->onMaterial;
    }
}
