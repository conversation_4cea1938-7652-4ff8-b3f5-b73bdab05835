<?php

declare(strict_types=1);

namespace App\Domain\Model\Plasterer\PriceIndication\Command;

use App\Domain\Model\Address;
use App\Domain\Model\Plasterer\Specification\Dto\SpecificationDto;
use App\Domain\Model\PriceIndication\PriceIndicationType;
use App\Domain\Validator\Year;
use Symfony\Component\Validator\Constraints as Assert;

final readonly class CreatePlastererPriceIndicationCommand
{
    public function __construct(
        #[Assert\NotBlank]
        public string $firstName,

        #[Assert\NotBlank]
        public string $lastName,

        public Address $address,

        #[Assert\NotBlank]
        #[Year]
        public int $constructionYear,

        #[Assert\NotBlank]
        public string $phoneNumber,

        #[Assert\Email]
        #[Assert\NotBlank]
        public string $emailAddress,

        #[Assert\Length(max: 1000)]
        public ?string $description,

        #[Assert\NotBlank]
        public string $source,

        #[Assert\NotBlank]
        #[Assert\Valid]
        public SpecificationDto $specification,

        public ?string $subdomain,

        public PriceIndicationType $type,
    ) {
    }
}
