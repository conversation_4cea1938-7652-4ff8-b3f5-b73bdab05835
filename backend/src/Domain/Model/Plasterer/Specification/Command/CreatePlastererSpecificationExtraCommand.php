<?php

declare(strict_types=1);

namespace App\Domain\Model\Plasterer\Specification\Command;

use App\Domain\Model\Plasterer\Specification\Specification;
use App\Domain\Model\Vat\VatPercentage;
use Money\Money;

final readonly class CreatePlastererSpecificationExtraCommand
{
    public function __construct(
        public Specification $specification,
        public VatPercentage $vatPercentage,
        public string $name,
        public bool $provisional,
        public Money $price,
    ) {
    }

    public function getSpecification(): Specification
    {
        return $this->specification;
    }
}
