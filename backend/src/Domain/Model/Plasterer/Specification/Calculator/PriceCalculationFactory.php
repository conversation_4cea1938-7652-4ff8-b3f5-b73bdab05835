<?php

declare(strict_types=1);

namespace App\Domain\Model\Plasterer\Specification\Calculator;

use App\Domain\Model\Plasterer\Handling\Handling;
use App\Domain\Model\Plasterer\Handling\Repository\HandlingRepositoryInterface;
use App\Domain\Model\Plasterer\Specification\Specification;
use App\Domain\Model\Plasterer\Specification\SpecificationLocalityPlane;

final readonly class PriceCalculationFactory
{
    private ?Handling $disposalHandling;
    private ?Handling $tapingHandling;
    private ?Handling $floorCoveringHandling;
    private ?Handling $outerCornerHandling;
    private ?Handling $innerCornerHandling;
    private ?Handling $stopCornerHandling;

    public function __construct(private HandlingRepositoryInterface $handlingRepository)
    {
        $this->disposalHandling = $this->handlingRepository->findByIdentifier(Handling::DISPOSAL_IDENTIFIER);
        $this->tapingHandling = $this->handlingRepository->findByIdentifier(Handling::TAPING_IDENTIFIER);
        $this->floorCoveringHandling = $this->handlingRepository->findByIdentifier(Handling::FLOOR_COVERING_IDENTIFIER);
        $this->outerCornerHandling = $this->handlingRepository->findByIdentifier(Handling::OUTER_CORNER_IDENTIFIER);
        $this->innerCornerHandling = $this->handlingRepository->findByIdentifier(Handling::INNER_CORNER_IDENTIFIER);
        $this->stopCornerHandling = $this->handlingRepository->findByIdentifier(Handling::STOP_CORNER_IDENTIFIER);
    }

    public function create(Specification $specification): PriceCalculation
    {
        $handlingMaterialAmountMapping = $specification->getSpecificationHandlingMaterialAmountMapping();

        $priceCalculation = new PriceCalculation($specification);
        foreach ($specification->getSpecificationLocalities() as $specificationLocality) {
            $priceCalculationLocality = new PriceCalculationLocality($specificationLocality);

            foreach ($specificationLocality->getSpecificationLocalityPlanes() as $specificationLocalityPlane) {
                $priceCalculationLocalityPlane = new PriceCalculationLocalityPlane($specificationLocalityPlane);

                foreach ($this->getHandlingsForPlane($specificationLocalityPlane) as $handling) {
                    $priceCalculationLocalityPlaneHandling = new PriceCalculationLocalityPlaneHandling($handling);

                    foreach ($handlingMaterialAmountMapping[$handling->getId()] as $materialAmount) {
                        $materialType = $materialAmount->getMaterial()->getMaterialType();

                        // skip if the material type for correct plane
                        if (null !== $materialAmount->getMaterial()->getPlane() && $materialAmount->getMaterial()->getPlane()->getId() !== $specificationLocalityPlane->getPlane()->getId()) {
                            continue;
                        }

                        $localityModifier = $specificationLocality->getLocality()->getCalculationModifier($materialType);
                        $productModifier = $specificationLocalityPlane->getProduct()->getCalculationModifier($materialType);

                        $value = match ($materialAmount->getHandling()->getIdentifier()) {
                            Handling::OUTER_CORNER_IDENTIFIER => $specificationLocalityPlane->getOuterCorners(),
                            Handling::INNER_CORNER_IDENTIFIER => $specificationLocalityPlane->getInnerCorners(),
                            Handling::STOP_CORNER_IDENTIFIER => $specificationLocalityPlane->getStopCorners(),
                            default => $specificationLocalityPlane->getSquareMeters(),
                        };

                        $amountModifiers = [];
                        if (\is_float($localityModifier)) {
                            $amountModifiers[] = new AmountModifier('Locality modifier', $localityModifier);
                        }

                        if (\is_float($productModifier)) {
                            $amountModifiers[] = new AmountModifier('Product modifier', $productModifier);
                        }

                        $priceCalculationLocalityPlaneHandling->addPriceCalculationLocalityPlaneHandlingMaterial(
                            new PriceCalculationLocalityPlaneHandlingMaterial(
                                $materialAmount->getMaterial()->getMaterialType(),
                                $value ?? 0,
                                $materialAmount->getAmount(),
                                $amountModifiers,
                            )
                        );
                    }
                    $priceCalculationLocalityPlane->addPriceCalculationLocalityPlaneHandling($priceCalculationLocalityPlaneHandling);
                }
                $priceCalculationLocality->addPriceCalculationLocalityPlane($priceCalculationLocalityPlane);
            }
            $priceCalculation->addPriceCalculationLocality($priceCalculationLocality);
        }

        return $priceCalculation;
    }

    /**
     * @return array<Handling>
     */
    public function getHandlingsForPlane(SpecificationLocalityPlane $specificationLocalityPlane): array
    {
        /** @var Handling[] $handlings */
        $handlings = [];

        $surface = $specificationLocalityPlane->getSurface();
        $product = $specificationLocalityPlane->getProduct();

        // add handling for the surface and product
        $handling = $surface->getHandlingByProduct($product);
        if ($handling instanceof Handling) {
            $handlings[] = $handling;
            $handlings[] = $this->tapingHandling;
            $handlings[] = $this->floorCoveringHandling;
        }

        // add handling for the surface and finish
        $classification = $specificationLocalityPlane->getFinish()->getClassification();
        $handling = $surface->getHandlingGroup()->getHandlingByClassification($classification);
        if ($handling instanceof Handling) {
            $handlings[] = $handling;
            $handlings[] = $this->disposalHandling;
        }

        // add handling for the surface and plane
        $handling = $specificationLocalityPlane->getPlane()->getHandling();
        if ($handling instanceof Handling) {
            $handlings[] = $handling;
        }

        if ($specificationLocalityPlane->getOuterCorners() > 0) {
            $handlings[] = $this->outerCornerHandling;
        }

        if ($specificationLocalityPlane->getInnerCorners() > 0) {
            $handlings[] = $this->innerCornerHandling;
        }

        if ($specificationLocalityPlane->getStopCorners() > 0) {
            $handlings[] = $this->stopCornerHandling;
        }

        return array_filter($handlings);
    }
}
