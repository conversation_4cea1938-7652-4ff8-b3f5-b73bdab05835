<?php

declare(strict_types=1);

namespace App\Domain\Model\Plasterer\Product;

use App\Domain\Model\Plasterer\Locality\Locality;
use App\Domain\Model\Plasterer\Plane\Plane;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'plasterer_product_option')]
class ProductOption
{
    #[ORM\Id]
    #[ORM\ManyToOne(targetEntity: Locality::class)]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private Locality $locality;

    #[ORM\Id]
    #[ORM\ManyToOne(targetEntity: Plane::class)]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private Plane $plane;

    #[ORM\Id]
    #[ORM\ManyToOne(targetEntity: Product::class, inversedBy: 'productOptions')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private Product $product;

    public function __construct(Locality $locality, Plane $plane, Product $product)
    {
        $this->locality = $locality;
        $this->plane = $plane;
        $this->product = $product;
    }

    public function getLocality(): Locality
    {
        return $this->locality;
    }

    public function getPlane(): Plane
    {
        return $this->plane;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }
}
