<?php

declare(strict_types=1);

namespace App\Domain\Model\Plasterer\Quotation\Factory\PDF;

use App\Domain\Model\Discipline\DisciplineIdentifier;
use App\Domain\Model\File\File;
use App\Domain\Model\Plasterer\Quotation\Quotation;
use Knp\Snappy\Pdf;
use Twig\Environment;
use Zenstruck\Filesystem;

final readonly class QuotationPdfFileFactory
{
    public function __construct(
        private Pdf $pdfGenerator,
        private Filesystem $defaultFilesystem,
        private Environment $twig,
        private PdfOutputOptionsFactory $quotationPDFOptionsFactory,
    ) {
    }

    public function create(Quotation $quotation): File
    {
        $html = $this->twig->render('pdf/plasterer/quotation.html.twig', [
            'quotation' => $quotation,
            'primaryColor' => $quotation->getContractor()->getWebsiteSettingsForDiscipline(DisciplineIdentifier::Plasterer)?->getPrimaryColor() ?? '#FF5336',
        ]);

        $options = $this->quotationPDFOptionsFactory->create($quotation);
        $pdfContent = $this->pdfGenerator->getOutputFromHtml($html, $options);

        $dir = "/plasterer/quotation/{$quotation->getId()}";
        $fileName = "{$quotation->getFormattedNumber()}.pdf";

        $fileSystemFile = $this->defaultFilesystem->write("$dir/$fileName", $pdfContent);

        return new File($fileSystemFile);
    }
}
