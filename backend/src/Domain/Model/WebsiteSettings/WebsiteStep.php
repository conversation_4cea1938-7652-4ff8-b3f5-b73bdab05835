<?php

declare(strict_types=1);

namespace App\Domain\Model\WebsiteSettings;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity]
class WebsiteStep
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: WebsiteSettings::class, inversedBy: 'steps')]
    private ?WebsiteSettings $websiteSettings = null;

    #[ORM\Column(type: Types::STRING)]
    #[Groups(['website_settings:read'])]
    private string $title;

    #[ORM\Column(type: Types::TEXT)]
    #[Groups(['website_settings:read'])]
    private string $text;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    #[Groups(['website_settings:read'])]
    private ?string $ctaUrl;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    #[Groups(['website_settings:read'])]
    private ?string $ctaText;

    public function __construct(
        string $title,
        string $text,
        ?string $ctaUrl,
        ?string $ctaText,
    ) {
        $this->title = $title;
        $this->text = $text;
        $this->ctaUrl = $ctaUrl;
        $this->ctaText = $ctaText;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function getCtaUrl(): ?string
    {
        return $this->ctaUrl;
    }

    public function getCtaText(): ?string
    {
        return $this->ctaText;
    }

    public function setWebsiteSettings(WebsiteSettings $websiteSettings): void
    {
        $this->websiteSettings = $websiteSettings;
    }

    /**
     * @return array<string, string>
     */
    public static function CtaHrefOptions(): array
    {
        return [
            'Aanvraag starten' => '#aanvraag-starten',
            'Voordelen' => '#voordelen',
            'Werkwijze' => '#werkwijze',
            'Over ons' => '#over-ons',
        ];
    }
}
