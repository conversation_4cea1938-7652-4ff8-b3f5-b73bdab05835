<?php

declare(strict_types=1);

namespace App\Domain\Model\Quotation\Command;

use App\Domain\Exception\DomainException;
use App\Domain\Model\Quotation\AbstractQuotation;
use App\Domain\Model\Quotation\Event\QuotationCancelled;
use App\Domain\Model\User\User;
use App\Domain\RepositoryInterface;
use App\Domain\Resolver;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
final readonly class CancelQuotationCommandHandler
{
    public function __construct(
        private Resolver $resolver,
        private RepositoryInterface $repository,
        private MessageBusInterface $messageBus,
    ) {
    }

    public function __invoke(CancelQuotationCommand $command): AbstractQuotation
    {
        $quotation = $this->resolver->resolve(AbstractQuotation::class, $command->quotationId);

        if ($quotation->isCancelled()) {
            throw new DomainException('Quotation is already cancelled.');
        }

        if ($command->userId instanceof UuidInterface) {
            $user = $this->resolver->resolve(User::class, $command->userId);
        }

        $quotation->cancel($command->cancelReason, $command->cancelDescription, $user ?? null);

        $this->messageBus->dispatch(new QuotationCancelled($quotation->getId()));

        $this->repository->save();

        return $quotation;
    }
}
