<?php

declare(strict_types=1);

namespace App\Domain\Model\PriceIndication\Command;

use App\Domain\Attribute\MapCurrentUser;
use App\Domain\Attribute\MapParameter;
use Ramsey\Uuid\UuidInterface;

final readonly class CompletePriceIndicationCommand
{
    public function __construct(
        #[MapParameter(key: 'id')]
        public UuidInterface $priceIndicationId,
        #[MapCurrentUser(propertyPath: 'id')]
        public UuidInterface $userId,
        public ?string $completedMessage,
    ) {
    }
}
