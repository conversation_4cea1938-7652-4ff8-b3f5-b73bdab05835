<?php

declare(strict_types=1);

namespace App\Domain\Model\PriceIndication\Command;

use App\Domain\Model\PriceIndication\PriceIndication;
use App\Domain\Model\User\User;
use App\Domain\RepositoryInterface;
use App\Domain\Resolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CompletePriceIndicationCommandHandler
{
    public function __construct(
        private Resolver $resolver,
        private RepositoryInterface $repository,
    ) {
    }

    public function __invoke(CompletePriceIndicationCommand $command): void
    {
        $priceIndication = $this->resolver->resolve(PriceIndication::class, $command->priceIndicationId);
        $user = $this->resolver->resolve(User::class, $command->userId);

        $priceIndication->complete($command->completedMessage, $user);

        $this->repository->save();
    }
}
