<?php

declare(strict_types=1);

namespace App\Domain\Model\PriceIndication;

use App\Domain\Model\User\User;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class PriceIndicationView
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private User $viewedBy;

    #[ORM\ManyToOne(targetEntity: PriceIndication::class, inversedBy: 'views')]
    #[ORM\JoinColumn(nullable: false)]
    private PriceIndication $priceIndication;

    #[ORM\Column(type: Types::STRING, enumType: PriceIndicationStatus::class)]
    private PriceIndicationStatus $status;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private DateTimeImmutable $viewedAt;

    public function __construct(PriceIndication $priceIndication, User $viewedBy)
    {
        $this->priceIndication = $priceIndication;
        $this->viewedBy = $viewedBy;
        $this->status = $this->priceIndication->getStatus();
        $this->viewedAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getViewedBy(): ?User
    {
        return $this->viewedBy;
    }

    public function getPriceIndication(): PriceIndication
    {
        return $this->priceIndication;
    }

    public function getViewedAt(): DateTimeImmutable
    {
        return $this->viewedAt;
    }

    public function getStatus(): PriceIndicationStatus
    {
        return $this->status;
    }
}
