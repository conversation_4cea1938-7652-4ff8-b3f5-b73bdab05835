<?php

declare(strict_types=1);

namespace App\Domain\Model\Painter\Quotation\Factory\PDF;

use App\Domain\Model\Painter\Quotation\Quotation;
use Knp\Snappy\Pdf;
use RuntimeException;
use Symfony\Component\Mime\Part\DataPart;
use Twig\Environment;

final readonly class MjopInfoPdfDataPartFactory
{
    public function __construct(
        private Pdf $pdfGenerator,
        private Environment $twig,
        private PdfOutputOptionsFactory $quotationPDFOptionsFactory,
    ) {
    }

    public function create(Quotation $quotation): DataPart
    {
        $html = $this->twig->render('pdf/painter/mjop_info.html.twig');
        $pdfOptions = $this->quotationPDFOptionsFactory->create($quotation);
        $data = $this->pdfGenerator->getOutputFromHtml($html, $pdfOptions);

        $tempStream = fopen('php://temp', 'r+');

        if (!$tempStream) {
            throw new RuntimeException('Failed to open temporary stream for PDF data.');
        }

        fwrite($tempStream, $data);
        rewind($tempStream);

        return new DataPart($tempStream, 'MeerJarenOnderhoudsPlan (MJOP).pdf', 'application/pdf');
    }
}
