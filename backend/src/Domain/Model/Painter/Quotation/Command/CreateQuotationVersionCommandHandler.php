<?php

declare(strict_types=1);

namespace App\Domain\Model\Painter\Quotation\Command;

use App\Domain\Model\Painter\Quotation\Factory\QuotationFactory;
use App\Domain\Model\Painter\Quotation\Quotation;
use App\Domain\Model\Specification\Command\CalculateSpecificationCommand;
use App\Domain\Resolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
final readonly class CreateQuotationVersionCommandHandler
{
    public function __construct(
        private Resolver $resolver,
        private QuotationFactory $quotationFactory,
        private MessageBusInterface $messageBus,
    ) {
    }

    public function __invoke(CreateQuotationVersionCommand $command): Quotation
    {
        $quotation = $this->resolver->resolve(Quotation::class, $command->quotationId);

        $newQuotationVersion = $this->quotationFactory->createVersion($quotation);

        $this->messageBus->dispatch(new CalculateSpecificationCommand($newQuotationVersion->getSpecification()->getId()));

        return $newQuotationVersion;
    }
}
