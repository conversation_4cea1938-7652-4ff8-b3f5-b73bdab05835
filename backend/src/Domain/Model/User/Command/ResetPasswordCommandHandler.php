<?php

declare(strict_types=1);

namespace App\Domain\Model\User\Command;

use App\Domain\Model\User\Repository\UserRepositoryInterface;
use App\Domain\Model\User\Resolver\TokenResolver;
use App\Domain\Model\User\TokenType;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsMessageHandler]
final readonly class ResetPasswordCommandHandler
{
    public function __construct(
        private UserPasswordHasherInterface $userPasswordHasher,
        private UserRepositoryInterface $repository,
        private TokenResolver $tokenResolver,
    ) {
    }

    public function __invoke(ResetPasswordCommand $command): void
    {
        $token = $this->tokenResolver->resolveOneByToken($command->token);

        $token->consume(TokenType::ResetPassword);

        $user = $token->getUser();
        $user->setPassword($this->userPasswordHasher->hashPassword($user, $command->password));

        $this->repository->save($user);
    }
}
