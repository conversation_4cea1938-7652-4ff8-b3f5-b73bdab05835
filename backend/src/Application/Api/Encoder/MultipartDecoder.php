<?php

declare(strict_types=1);

namespace App\Application\Api\Encoder;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Serializer\Encoder\DecoderInterface;

final readonly class MultipartDecoder implements DecoderInterface
{
    public const FORMAT = 'multipart';

    public function __construct(private RequestStack $requestStack)
    {
    }

    /**
     * @param array<string, mixed> $context
     *
     * @return array<mixed>|null
     */
    public function decode(string $data, string $format, array $context = []): ?array
    {
        $request = $this->requestStack->getCurrentRequest();

        if (!$request instanceof Request) {
            return null;
        }

        return array_map(static function (array|bool|float|int|string $element) {
            if (\is_string($element)) {
                return json_decode($element, true, flags: \JSON_THROW_ON_ERROR);
            }

            // Multipart form values will be encoded in JSON.
        }, $request->request->all()) + $request->files->all();
    }

    public function supportsDecoding(string $format): bool
    {
        return self::FORMAT === $format;
    }
}
