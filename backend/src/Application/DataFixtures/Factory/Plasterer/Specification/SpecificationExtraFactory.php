<?php

declare(strict_types=1);

namespace App\Application\DataFixtures\Factory\Plasterer\Specification;

use App\Domain\Model\Plasterer\Specification\SpecificationExtra;
use App\Domain\Model\Vat\VatPercentage;
use Money\Money;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<SpecificationExtra>
 */
final class SpecificationExtraFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     *
     * @return array<string, mixed>
     */
    protected function defaults(): array
    {
        return [
            'specification' => SpecificationFactory::new()->create(),
            'name' => self::faker()->text(),
            'vatPercentage' => VatPercentage::VatPercentage9,
            'provisional' => self::faker()->boolean(),
            'price' => Money::EUR(self::faker()->numberBetween(1000, 1000000)),
        ];
    }

    public static function class(): string
    {
        return SpecificationExtra::class;
    }
}
