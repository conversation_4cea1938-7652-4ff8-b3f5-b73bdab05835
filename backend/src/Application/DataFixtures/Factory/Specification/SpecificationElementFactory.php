<?php

declare(strict_types=1);

namespace App\Application\DataFixtures\Factory\Specification;

use App\Application\DataFixtures\Factory\Element\ElementFactory;
use App\Application\DataFixtures\Factory\ElementGroup\ElementGroupFactory;
use App\Domain\Model\Specification\SpecificationElement;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;

/**
 * @extends PersistentProxyObjectFactory<SpecificationElement>
 */
final class SpecificationElementFactory extends PersistentProxyObjectFactory
{
    /**
     * @see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories
     **
     * @return array<string, mixed>
     */
    protected function defaults(): array
    {
        return [
            'element' => ElementFactory::new()->create(),
            'elementGroup' => ElementGroupFactory::new()->create(),
            'quantity' => self::faker()->randomFloat(0, 1, 9),
        ];
    }

    public static function class(): string
    {
        return SpecificationElement::class;
    }
}
