<?php

declare(strict_types=1);

namespace App\Application\Admin\Form;

use App\Application\Admin\Request\Input\WebsiteStepInput;
use App\Domain\Model\WebsiteSettings\WebsiteStep;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class WebsiteStepType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, ['label' => 'global.label_title'])
            ->add('text', TextType::class, ['label' => 'global.label_text'])
            ->add('ctaUrl', ChoiceType::class, [
                'label' => 'global.label_cta_url',
                'required' => false,
                'choices' => WebsiteStep::CtaHrefOptions(),
            ])
            ->add('ctaText', TextType::class, ['label' => 'global.label_cta_text', 'required' => false]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => WebsiteStepInput::class,
            'translation_domain' => 'admin',
        ]);
    }
}
