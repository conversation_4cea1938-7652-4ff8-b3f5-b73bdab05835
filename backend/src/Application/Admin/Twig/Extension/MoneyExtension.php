<?php

declare(strict_types=1);

namespace App\Application\Admin\Twig\Extension;

use Money\Currencies\ISOCurrencies;
use Money\Formatter\DecimalMoneyFormatter;
use Money\Formatter\IntlMoneyFormatter;
use Money\Money;
use NumberFormatter;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

final class MoneyExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('money_format', [$this, 'formatMoney']),
        ];
    }

    public function formatMoney(?Money $money = null, bool $decimal = true, bool $symbol = true): string
    {
        if (!$money instanceof Money) {
            return '';
        }

        $formatter = $symbol ? $this->getMoneyFormatter() : new DecimalMoneyFormatter($this->getCurrencies());

        $formatted = $formatter->format($money);

        if (!$decimal) {
            return $this->removeDecimal($formatted);
        }

        return $formatted;
    }

    private function getMoneyFormatter(): IntlMoneyFormatter
    {
        return new IntlMoneyFormatter(
            new NumberFormatter('nl', NumberFormatter::CURRENCY),
            $this->getCurrencies()
        );
    }

    private function getCurrencies(): ISOCurrencies
    {
        return new ISOCurrencies();
    }

    private function removeDecimal(string $formatted): string
    {
        return mb_substr($formatted, 0, mb_strlen($formatted) - 3);
    }
}
