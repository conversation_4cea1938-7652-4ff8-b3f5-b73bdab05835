<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Contractor;

use App\Application\Admin\Form\OrderedSelect2JsEntityType;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\ContractorInput;
use App\Domain\Model\Contractor\Command\UpdateContractorCommand;
use App\Domain\Model\Contractor\Contractor;
use App\Domain\Model\Discipline\Discipline;
use App\Domain\Model\Partner\IndustryAssociationPartner;
use App\Domain\Model\Partner\MaintenancePlanPartner;
use App\Domain\Model\PostalCode\PostalCode;
use FH\Bundle\AdminBundle\Admin\Admin;
use FH\Bundle\AdminBundle\Admin\LifecycleMiddlewareInterface;
use FH\Bundle\AdminBundle\Admin\LifecycleMiddlewareTrait;
use FH\Bundle\AdminBundle\Controller\CRUDController;
use FH\Bundle\AdminBundle\Route\RouteBuilderTrait;
use FH\Bundle\AdminBundle\Translator\UnderscoreGlobalLabelStrategy;
use FH\Bundle\FormBundle\Form\Type\SingleFileType;
use FH\Bundle\FormBundle\Form\Type\SingleImageUploadType;
use Knp\Menu\ItemInterface;
use RuntimeException;
use Sonata\AdminBundle\Admin\AdminInterface;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Validator\Constraints\Count;

/**
 * @extends Admin<Contractor>
 *
 * @implements LifecycleMiddlewareInterface<Contractor, ContractorInput>
 *
 * @method Contractor getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_contractors',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Contractor::class,
            'name' => 'sonata.admin',
            'group' => 'fh_admin.group.contractors',
            'translation_domain' => 'admin',
            'icon' => 'fas fa-building',
            'on_top' => true,
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\Component\Messenger\MessageBusInterface']],
        ['addChild', ['@App\Application\Admin\Admin\Settings\User\UserAdmin', 'contractor']],
        ['addChild', ['@App\Application\Admin\Admin\Painter\MaterialPrice\MaterialPriceAdmin', 'contractor']],
        ['addChild', ['@App\Application\Admin\Admin\Settings\WebsiteSettings\WebsiteSettingsAdmin', 'contractor']],
        ['addChild', ['@App\Application\Admin\Admin\Plasterer\HourPrice\HourPriceAdmin', 'contractor']],
    ],
)]
class ContractorAdmin extends Admin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Contractor, ContractorInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;
    use RouteBuilderTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_contractor';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'contractor';
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with('contractor_details')
                ->add('name', null, ['label' => 'global.label_company_name'])
                ->add('cocNumber')
                ->add('vatNumber')
                ->add('iban')
                ->add('ibanName')
            ->end()
            ->with('address')
                ->add('address.street')
                ->add('address.houseNumber')
                ->add('address.houseNumberSuffix')
                ->add('address.postalCode')
                ->add('address.city')
            ->end()
            ->with('contact_details')
                ->add('contactEmail')
                ->add('contactPhone')
            ->end()
            ->with('settings')
                ->add('disciplines', OrderedSelect2JsEntityType::class, [
                    'class' => Discipline::class,
                    'multiple' => true,
                    'required' => false,
                    'order_by' => 'name',
                    'constraints' => [new Count(min: 1)],
                ])
                ->add('postalCodes', OrderedSelect2JsEntityType::class, [
                    'class' => PostalCode::class,
                    'multiple' => true,
                    'required' => false,
                    'order_by' => 'prefix',
                ])
                ->add('associationPartner', OrderedSelect2JsEntityType::class, [
                    'class' => IndustryAssociationPartner::class,
                    'required' => false,
                    'order_by' => 'name',
                ])
                ->add('maintenancePartner', OrderedSelect2JsEntityType::class, [
                    'class' => MaintenancePlanPartner::class,
                    'required' => false,
                    'order_by' => 'name',
                ])
                ->add('privacyStatement', SingleFileType::class, [
                    'oneup_id' => 'contractor_privacy_statement',
                    'label' => 'global.label_privacy_statement_pdf',
                    'required' => false,
                ])
                ->add('termsAndConditions', SingleFileType::class, [
                    'oneup_id' => 'contractor_terms_and_conditions',
                    'label' => 'global.label_terms_and_conditions_pdf',
                    'required' => false,
                ])
                ->add('logo', SingleImageUploadType::class, [
                    'image_upload_set' => 'contractor_logo',
                    'help' => 'global.help_logo',
                ])
            ->end();
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $this->addAdminRoutes($collection, [
            'list',
            'edit',
        ]);

        $collection->remove('show');
        $collection->remove('create');
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('address.city')
            ->add('cocNumber')
            ->add('user.email', fieldDescriptionOptions: ['label' => 'global.label_login_user'])
            ->add('contactEmail')
            ->add('contactPhone')
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('name')
            ->add('address.city')
            ->add('cocNumber')
            ->add('user.email', fieldDescriptionOptions: ['label' => 'global.label_login_user'])
            ->add('contactEmail')
            ->add('contactPhone')
        ;
    }

    protected function configureTabMenu(ItemInterface $menu, string $action, ?AdminInterface $childAdmin = null): void
    {
        if (!$childAdmin && 'edit' !== $action) {
            return;
        }

        /** @var Contractor $contractor */
        $contractor = $this->getSubject();

        $menu->addChild('global.label_contractor', [
            'uri' => $this->generateObjectUrl('edit', $contractor),
        ]);

        $menu->addChild('global.label_user', [
            'route' => 'admin_contractor_admin_user_edit',
            'routeParameters' => [
                'id' => $contractor->getId(),
                'childId' => $contractor->getUser()->getId(),
            ],
        ]);

        $admin = $this->isChild() ? $this->getParent() : $this;
        $id = $admin->getRequest()->get('id');

        if ($contractor->isPainter()) {
            $menu->addChild('global.label_material_prices', [
                'route' => 'admin_contractor_admin_material_price_list',
                'routeParameters' => ['id' => $id],
            ]);
        }

        if ($contractor->isPlasterer()) {
            $menu->addChild('global.label_hour_price', [
                'route' => 'admin_contractor_admin_hour_price_list',
                'routeParameters' => ['id' => $id],
            ]);
        }

        $menu->addChild('global.label_website_settings', [
            'route' => 'admin_contractor_admin_website_settings_list',
            'routeParameters' => ['id' => $id],
        ]);
    }

    protected function configureExportFields(): array
    {
        return [
            'name',
            'cocNumber',
            'vatNumber',
            'iban',
            'ibanName',
            'contactEmail',
            'contactPhone',
        ];
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return ContractorInput::class;
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        throw new RuntimeException('Not implemented');
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return ContractorInput::createFromEntity($entity);
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new UpdateContractorCommand(
            $this->getSubject()->getId(),
            $submittedData->getName(),
            $submittedData->getAddress()->toDomainModel(),
            $submittedData->getContactEmail(),
            $submittedData->getContactPhone(),
            $submittedData->getCocNumber(),
            $submittedData->getVatNumber(),
            $submittedData->getIban(),
            $submittedData->getIbanName(),
            $submittedData->getPostalCodes(),
            $submittedData->getDisciplines(),
            $submittedData->getAssociationPartner(),
            $submittedData->getMaintenancePartner(),
            $submittedData->getPrivacyStatement(),
            $submittedData->getTermsAndConditions(),
            $submittedData->getLogo(),
        );
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Contractor
    {
        $contractor = $this->lifecycleMiddlewareDispatchMessage($editable);

        if (!$contractor instanceof Contractor) {
            throw new RuntimeException('Expected a Contractor instance');
        }

        return $contractor;
    }
}
