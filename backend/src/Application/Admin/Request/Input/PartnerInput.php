<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Model\Partner\MaintenancePlanPartner;
use App\Domain\Model\Partner\Partner;
use App\Domain\Model\Partner\PartnerIdentifier;
use App\Domain\Model\Partner\PartnerType;
use Money\Money;
use Symfony\Component\Validator\Constraints as Assert;

final class PartnerInput
{
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $name = null;

    public ?AddressInput $address = null;

    #[Assert\Length(max: 255)]
    #[Assert\Email]
    #[Assert\NotBlank]
    public ?string $email = null;

    #[Assert\Length(max: 255)]
    public ?string $phone = null;

    #[Assert\Length(max: 255)]
    #[Assert\Url]
    public ?string $websiteUrl = null;

    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $contactName = null;

    #[Assert\Length(max: 255)]
    #[Assert\Email]
    #[Assert\NotBlank]
    public ?string $contactEmail = null;

    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $contactPhone = null;

    public ?PartnerIdentifier $identifier = null;

    public ?PartnerType $type = null;

    #[Assert\Length(max: 255)]
    public ?string $termsAndConditions = null;

    public ?Money $paymentCosts = null;

    public function __construct()
    {
    }

    public static function createFromEntity(Partner $partner): self
    {
        $input = new self();
        $input->name = $partner->getName();
        $input->address = AddressInput::createFromEntity($partner->getAddress());
        $input->email = $partner->getEmail();
        $input->phone = $partner->getPhone();
        $input->websiteUrl = $partner->getWebsiteUrl();
        $input->contactName = $partner->getContactName();
        $input->contactEmail = $partner->getContactEmail();
        $input->contactPhone = $partner->getContactPhone();
        $input->identifier = $partner->getIdentifier();
        $input->type = $partner->getType();
        $input->termsAndConditions = $partner->getTermsAndConditions();

        if ($partner instanceof MaintenancePlanPartner) {
            $input->paymentCosts = $partner->getPaymentCosts();
        }

        return $input;
    }
}
