<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Model\Contractor\Contractor;
use App\Domain\Model\Plasterer\Handling\Handling;
use App\Domain\Model\Plasterer\HandlingMaterialAmount\HandlingMaterialAmount;
use App\Domain\Model\Plasterer\Material\Material;
use Symfony\Component\Validator\Constraints as Assert;

final class HandlingMaterialAmountInput
{
    #[Assert\NotBlank]
    public ?float $amount = null;

    #[Assert\NotBlank]
    public ?Material $material = null;

    #[Assert\NotBlank]
    public ?Handling $handling = null;

    public ?Contractor $contractor = null;

    public static function createFromEntity(HandlingMaterialAmount $handlingMaterialAmount): self
    {
        $input = new self();

        $input->contractor = $handlingMaterialAmount->getContractor();
        $input->handling = $handlingMaterialAmount->getHandling();
        $input->material = $handlingMaterialAmount->getMaterial();
        $input->amount = $handlingMaterialAmount->getAmount();

        return $input;
    }
}
