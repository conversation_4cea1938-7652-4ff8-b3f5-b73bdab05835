<?php

declare(strict_types=1);

namespace App\Application\Email;

use App\Domain\Model\Contractor\Contractor;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class ContractorRegisteredContractorEmailFactory
{
    public function __construct(
        private BaseEmailFactory $baseEmailFactory,
        private TranslatorInterface $translator,
        #[Autowire(env: 'PLANBUILDING_CUSTOMER_SUPPORT_EMAIL')]
        private string $planbuildingCustomerSupportEmail,
        #[Autowire(param: 'planbuilding_customer_support_phone_number')]
        private string $planbuildingCustomerSupportPhoneNumber,
    ) {
    }

    public function create(Contractor $contractor): TemplatedEmail
    {
        return $this->baseEmailFactory->createWithContext([
            'contractor' => $contractor,
            'customerSupportPhoneNumber' => $this->planbuildingCustomerSupportPhoneNumber,
        ])
            ->htmlTemplate('email/to_contractor/contractor_registered_contractor/contractor_registered_contractor.html.twig')
            ->textTemplate('email/to_contractor/contractor_registered_contractor/contractor_registered_contractor.txt.twig')
            ->subject($this->translator->trans('email.contractor_registered_contractor.subject'))
            ->from($this->planbuildingCustomerSupportEmail)
            ->addTo($contractor->getEmail());
    }
}
