<?php

declare(strict_types=1);

namespace App\Application\Email;

use App\Domain\Model\Contractor\Contractor;
use App\Domain\Model\Discipline\DisciplineIdentifier;
use Symfony\Component\Asset\Packages;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final class EmailThemeFactory
{
    private const DEFAULT_COLOR_PAINTER = '#FF5336';
    private const DEFAULT_COLOR_PLASTERER = '#4382EE';
    public const DEFAULT_LOGO_PAINTER = 'jouwlokaleschilder-logo.png';
    public const DEFAULT_FOOTER_LOGO_PAINTER = 'jouwlokaleschilder-logo-footer.png';
    public const DEFAULT_LOGO_PLASTERER = 'jouwlokalestukadoor-logo.png';
    public const DEFAULT_FOOTER_LOGO_PLASTERER = 'jouwlokalestukadoor-logo-footer.png';

    public function __construct(
        #[Autowire(param: 'assets_base_url')]
        private string $assetsBaseUrl,
        private Packages $packages,
    ) {
    }

    /**
     * @return array<string, string>
     */
    public function create(DisciplineIdentifier $disciplineIdentifier, ?Contractor $contractor = null): array
    {
        return [
            'logoUrl' => $this->getLogo($contractor, $disciplineIdentifier),
            'footerLogoUrl' => $this->getLogo(null, $disciplineIdentifier, true),
            'color' => $this->getColor($contractor, $disciplineIdentifier),
        ];
    }

    private function getColor(?Contractor $contractor, DisciplineIdentifier $disciplineIdentifier): string
    {
        $contractorColor = $contractor?->getWebsiteSettingsForDiscipline($disciplineIdentifier)?->getPrimaryColor();

        return $contractorColor ?? $this->getDefaultColorForDiscipline($disciplineIdentifier);
    }

    private function getLogo(?Contractor $contractor, DisciplineIdentifier $disciplineIdentifier, bool $footerLogo = false): string
    {
        return $contractor?->getLogo() ?
            $this->assetsBaseUrl.$this->packages->getUrl($contractor->getLogo(), 'contractor_logo') :
            $this->assetsBaseUrl.$this->packages->getUrl($this->getDefaultLogoForDiscipline($disciplineIdentifier, $footerLogo), 'email');
    }

    private function getDefaultLogoForDiscipline(DisciplineIdentifier $disciplineIdentifier, bool $footerLogo = false): string
    {
        if (DisciplineIdentifier::Plasterer === $disciplineIdentifier) {
            return $footerLogo
                ? self::DEFAULT_FOOTER_LOGO_PLASTERER
                : self::DEFAULT_LOGO_PLASTERER;
        }

        return $footerLogo
            ? self::DEFAULT_FOOTER_LOGO_PAINTER
            : self::DEFAULT_LOGO_PAINTER;
    }

    private function getDefaultColorForDiscipline(DisciplineIdentifier $disciplineIdentifier): string
    {
        return DisciplineIdentifier::Plasterer === $disciplineIdentifier ?
            self::DEFAULT_COLOR_PLASTERER :
            self::DEFAULT_COLOR_PAINTER;
    }
}
