<?php

declare(strict_types=1);

namespace App\Infrastructure\Mixpanel;

use RuntimeException;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final readonly class LookupTableUpdater
{
    public function __construct(
        private HttpClientInterface $mixpanelClient,
        private SerializerInterface $serializer,
        #[Autowire(env: 'MIXPANEL_PROJECT_ID')]
        private string $mixpanelProjectId,
    ) {
    }

    public function update(LookupTable $lookupTable): void
    {
        if ('' === $this->mixpanelProjectId) {
            throw new RuntimeException('MIXPANEL_PROJECT_ID is not set.');
        }

        $contents = $this->serializer->serialize($lookupTable->listings, 'csv');

        $this->mixpanelClient->request(
            method: Request::METHOD_PUT,
            url: "/lookup-tables/$lookupTable->lookupTableId",
            options: [
                'body' => $contents,
                'headers' => [
                    'Content-Type' => 'text/csv',
                ],
                'query' => [
                    'project_id' => $this->mixpanelProjectId,
                ],
            ],
        );
    }
}
