# ------------------------------------------------------
# https://docs.platform.sh/configuration/app.html
# ------------------------------------------------------

# https://docs.platform.sh/configuration/app/name.html
name: backend

# https://docs.platform.sh/configuration/app/timezone.html
timezone: "Europe/Amsterdam"

# https://docs.platform.sh/configuration/app/storage.html#disk
# Disc size in MB. Start small, storage isn't free
disk: 1536

# https://docs.platform.sh/create-apps/flexible-resources.html
size: S # 0.5 CPU

# memory = base_memory + (memory_ratio * CPU) = 512 + (768 * 0.5) = 896MB
resources:
    base_memory: 512
    memory_ratio: 768

# https://docs.platform.sh/configuration/app/type.html
# https://docs.platform.sh/languages/php.html
type: php:8.3

# https://docs.platform.sh/languages/php.html#build-flavor
# https://docs.platform.sh/configuration/app/build.html#build
build:
    flavor: composer

runtime:
    # https://docs.platform.sh/languages/php/extensions.html
    extensions:
        - sodium # lcobucci/jwt 4.3.0 requires ext-sodium
        - imagick

# https://docs.platform.sh/languages/php.html#expanded-dependencies
dependencies:
    php:
        composer/composer: '^2'
    ruby:
        "wkhtmltopdf-binary-ng": "> 0.12.6"

# https://docs.platform.sh/configuration/app/relationships.html
relationships:
    app_mariadb: 'mariadb:mysql'

# https://docs.platform.sh/configuration/app/variables.html
variables:
    env:
        APP_ENV: prod
        APP_DEBUG: false
        COMPOSER_NO_DEV: 1
        # https://docs.platform.sh/languages/nodejs/nvm.html#how-to-use-nvm-to-run-different-versions-of-nodejs
        NVM_VERSION: v0.38.0
        NODE_VERSION: v18

    # https://docs.platform.sh/languages/php/ini.html
    php:
        # https://www.php.net/manual/en/errorfunc.configuration.php#ini.display-errors
        display_errors: off
        # https://www.php.net/manual/en/errorfunc.configuration.php#ini.display-startup-errors
        display_startup_errors: off
        # https://www.php.net/manual/en/errorfunc.configuration.php#ini.error-reporting
        error_reporting: 22527
        # https://www.php.net/manual/en/ini.core.php#ini.post-max-size
        post_max_size: 16M
        # https://www.php.net/manual/en/ini.core.php#ini.upload-max-filesize
        upload_max_filesize: 8M
        # https://www.php.net/manual/en/ini.core.php#ini.memory-limit
        memory_limit: 256M
        # https://www.php.net/manual/en/datetime.configuration.php#ini.date.timezone
        date.timezone: 'Europe/Amsterdam'
        # https://www.php.net/manual/en/info.configuration.php#ini.max-execution-time
        max_execution_time: 60
        # https://www.php.net/manual/en/filesystem.configuration.php#ini.default-socket-timeout
        default_socket_timeout: 30

# https://docs.platform.sh/configuration/app/build.html#hooks
hooks:
    # https://docs.platform.sh/configuration/app/build.html#build-hook
    # https://docs.platform.sh/languages/nodejs/nvm.html#how-to-use-nvm-to-run-different-versions-of-nodejs
    build: |
        set -e

        composer dump-env prod

        # Link bundle assets
        bin/console assets:install --symlink --relative public

        # Build frontend assets
        bin/platform/build/build-frontend

        # It is currently not possible to prepare the cache in the build hook
        # The router cache depends on "env:FRONTEND_HOST", which is not available in the build hook
        #bin/platform/build/prepare-cache

        # Necessary so the first time this runs it wil unpack the correct binary into the /app folder
        # Since platform file-system is read-only by design this is only possible in the build hook.
        wkhtmltopdf -V

    # https://docs.platform.sh/configuration/app/build.html#deploy-hook
    deploy: |
        set -e

        bin/platform/deploy/warmup-cache

        # Enable when an external cache pool is used (for instance redis)
        #bin/console cache:pool:clear doctrine.result_cache_pool

        bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration

        # Generate JWT key pair for authentication
        bin/console lexik:jwt:generate-keypair --skip-if-exists

# https://docs.platform.sh/configuration/app/web.html
web:
    # https://docs.platform.sh/configuration/app/web.html#locations
    locations:
        '/':
            root: 'public'
            passthru: '/index.php'

            # Set the headers for static endpoints
            # If you wish to change this, also see the `headers` in web/index.php
            # https://docs.platform.sh/configuration/app/web.html#how-can-i-control-the-headers-sent-with-my-files
            headers:
                X-XSS-Protection: "1; mode=block"
                X-Content-Type-Options: nosniff
                X-Frame-Options: SAMEORIGIN
                Content-Security-Policy: "frame-ancestors 'none'"
                Access-Control-Allow-Credentials: "true"

            # rules are used (versus own location entry), so 404's will be handled by Symfony and default headers are applied
            rules:
                '^\/assets\/':
                    expires: 1h
                    scripts: false
                '^\/uploads\/':
                    expires: 1h
                    scripts: false
                # note that this set won't be cached by the router, but will be cached on the client side
                '^\/(apple-touch-icon\.png|favicon\.ico)$':
                    expires: 1h
                    scripts: false

# https://docs.platform.sh/configuration/app/storage.html#mounts
mounts:
    'var/cache':
        source: local
        source_path: 'cache'
    'var/log':
        source: local
        source_path: 'log'
    'var/sessions':
        source: local
        source_path: 'sessions'
    'var/secrets':
        source: local
        source_path: 'secrets'
    'var/import':
        source: local
        source_path: 'import'
    'var/storage':
        source: local
        source_path: 'storage'
    'public/uploads':
        source: local
        source_path: 'uploads'

# https://docs.platform.sh/configuration/app/cron.html
crons:
    mixpanel_update_lookup_tables:
        spec: '0 1 * * *'
        cmd: 'bin/console mixpanel:update-lookup-tables'

# https://docs.platform.sh/configuration/app/workers.html
# https://symfony.com/doc/current/messenger.html#consuming-messages-running-the-worker
workers:
    queue:
        # https://docs.platform.sh/create-apps/flexible-resources.html
        size: XS # 0.25 CPU
        disk: 128

        # memory = base_memory + (memory_ratio * CPU) = 128 + (128 * 0.25) = 160MB
        resources:
            base_memory: 128
            memory_ratio: 128

        commands:
            # starting a worker, handle 10 messages, restart, ...
            # So we don't run into problems with memory leaks
            start: rm -rf var/cache/* && php bin/console messenger:consume async --limit=10 --memory-limit=112M -vv
