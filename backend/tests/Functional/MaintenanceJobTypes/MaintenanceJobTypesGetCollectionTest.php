<?php

declare(strict_types=1);

namespace App\Tests\Functional\MaintenanceJobTypes;

use App\Application\DataFixtures\Factory\Contractor\ContractorFactory;
use App\Application\DataFixtures\Factory\MaintenancePlan\MaintenanceJobTypeFactory;
use App\Application\DataFixtures\Factory\MaintenancePlan\MaintenancePlanFactory;
use App\Domain\Model\Maintenance\MaintenancePlan;
use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\Response;

final class MaintenanceJobTypesGetCollectionTest extends ApiTestCase
{
    public function testAuthorized(): void
    {
        $maintenancePlan = MaintenancePlanFactory::createOne([
            'identifier' => MaintenancePlan::IDENTIFIER_MJOP,
        ]);

        MaintenanceJobTypeFactory::createMany(2, [
            'maintenancePlan' => $maintenancePlan,
        ]);

        $contractor = ContractorFactory::createOne();

        $json = $this->browser()
            ->actingAs($contractor->getUser())
            ->get('/backend/maintenance_job_types.json?maintenancePlan.identifier='.MaintenancePlan::IDENTIFIER_MJOP)
            ->assertSuccessful()
            ->json();

        self::assertJsonMatchesPattern($json, __DIR__.'/expected.collection.json');
    }

    public function testUnauthorized(): void
    {
        $this->browser()
            ->get('/backend/maintenance_job_types.json')
            ->assertStatus(Response::HTTP_UNAUTHORIZED);
    }
}
