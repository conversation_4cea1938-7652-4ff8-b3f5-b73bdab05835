<?php

declare(strict_types=1);

namespace App\Tests\Functional\Stats;

use App\Application\DataFixtures\Factory\Contractor\ContractorFactory;
use App\Application\DataFixtures\Factory\PriceIndication\PriceIndicationFactory;
use App\Domain\Model\Contractor\Contractor;
use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\Response;
use Zenstruck\Browser\KernelBrowser;

final class StatsGetTest extends ApiTestCase
{
    public function testEndpointForPainterDiscipline(): void
    {
        $json = $this->callEndpoint('painter')
            ->assertSuccessful()
            ->json();

        self::assertJsonMatchesPattern($json, __DIR__.'/expected.detail.json');
    }

    public function testEndpointForPlastererDiscipline(): void
    {
        $json = $this->callEndpoint('plasterer')
            ->assertSuccessful()
            ->json();

        self::assertJsonMatchesPattern($json, __DIR__.'/expected.detail.json');
    }

    public function testEndpointForNonExistingDiscipline(): void
    {
        $this->callEndpoint('developer')
            ->assertStatus(Response::HTTP_BAD_REQUEST);
    }

    public function testEndpointUnauthorized(): void
    {
        PriceIndicationFactory::createMany(2);

        $this->browser()
            ->get('/backend/stats.json')
            ->assertStatus(Response::HTTP_UNAUTHORIZED)
            ->json();
    }

    private function callEndpoint(string $discipline): KernelBrowser
    {
        return $this->browser()
            ->actingAs($this->createContractorWithPriceIndication()->getUser())
            ->get('/backend/stats.json', ['query' => ['discipline' => $discipline]]);
    }

    private function createContractorWithPriceIndication(): Contractor
    {
        $contractor = ContractorFactory::createOne()->_real();

        PriceIndicationFactory::createMany(2);
        PriceIndicationFactory::new()->withAssignedContractor($contractor)->create();

        return $contractor;
    }
}
