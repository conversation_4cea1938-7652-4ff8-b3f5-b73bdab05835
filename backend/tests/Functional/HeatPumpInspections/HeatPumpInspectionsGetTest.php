<?php

declare(strict_types=1);

namespace App\Tests\Functional\HeatPumpInspections;

use App\Application\DataFixtures\Factory\Order\Inspection\HeatPumpInspectionFactory;
use App\Application\DataFixtures\Factory\Order\OrderFactory;
use App\Domain\User\Roles;
use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class HeatPumpInspectionsGetTest extends ApiTestCase
{
    private const string URL = 'backend/heat_pump_inspections';

    public function testAuthenticatedUnauthorizedAdmin(): void
    {
        $object = HeatPumpInspectionFactory::createOne()->_real();

        $this->loginAs(Roles::ROLE_ADMIN);
        $this->client->jsonRequest(Request::METHOD_GET, \sprintf('%s/%s.json', self::URL, $object->getId()));

        self::assertResponseIsSuccessful();
        $this->assertResponseMatchesFile(__DIR__.'/expected.detail.json');
    }

    public function testAuthenticatedUnauthorizedSupplier(): void
    {
        $this->loginAs(Roles::ROLE_SUPPLIER);

        $object = HeatPumpInspectionFactory::createOne()->_real();
        $this->client->jsonRequest(Request::METHOD_GET, \sprintf('%s/%s.json', self::URL, $object->getId()));

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testAuthenticatedAuthorizedSupplier(): void
    {
        $user = $this->loginAs(Roles::ROLE_SUPPLIER);

        $order = OrderFactory::createOne(['supplier' => $user->getSupplier()])->_real();

        $inspection = HeatPumpInspectionFactory::createOne(['order' => $order])->_real();

        $this->client->jsonRequest(Request::METHOD_GET, \sprintf('%s/%s.json', self::URL, $inspection->getId()));

        self::assertResponseIsSuccessful();
        $this->assertResponseMatchesFile(__DIR__.'/expected.detail.json');
    }

    public function testUnauthenticated(): void
    {
        $object = HeatPumpInspectionFactory::createOne()->_real();

        $this->client->jsonRequest(Request::METHOD_GET, \sprintf('%s/%s.json', self::URL, $object->getId()));

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }
}
