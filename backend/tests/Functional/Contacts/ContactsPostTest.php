<?php

declare(strict_types=1);

namespace App\Tests\Functional\Contacts;

use App\Application\DataFixtures\Factory\Contractor\ContractorFactory;
use App\Domain\Model\Contact\ContactType;
use App\Tests\Functional\ApiTestCase;

final class ContactsPostTest extends ApiTestCase
{
    public function testEndpoint(): void
    {
        $contractor = ContractorFactory::createOne();

        $browser = $this->browser()
            ->actingAs($contractor->getUser())
            ->post('backend/contacts', [
                'json' => [
                    'firstName' => 'tester',
                    'lastName' => 'tester',
                    'address' => [
                        'postalCode' => '1234AB',
                        'houseNumber' => '1',
                        'houseNumberSuffix' => null,
                        'street' => 'Straatnaam',
                        'city' => 'Amsterdam',
                    ],
                    'phoneNumber' => '0612345678',
                    'emailAddress' => '<EMAIL>',
                    'comments' => 'Dit is een test',
                    'contractor' => '/contractors/'.$contractor->getId()->toString(),
                    'type' => ContactType::Consumer->value,
                ],
            ])
            ->assertSuccessful();

        self::assertJsonMatchesPattern($browser->json(), __DIR__.'/expected.detail.json');
    }
}
