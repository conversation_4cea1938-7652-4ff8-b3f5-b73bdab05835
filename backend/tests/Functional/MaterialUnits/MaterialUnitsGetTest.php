<?php

declare(strict_types=1);

namespace App\Tests\Functional\MaterialUnits;

use App\Application\DataFixtures\Factory\MaterialUnit\MaterialUnitFactory;
use App\Tests\Functional\ApiTestCase;

final class MaterialUnitsGetTest extends ApiTestCase
{
    public function testEndpoint(): void
    {
        $materialUnit = MaterialUnitFactory::createOne();

        $json = $this->browser()
            ->request('GET', \sprintf('/backend/material_units/%s.json', $materialUnit->getId()))
            ->assertSuccessful()
            ->json();

        self::assertJsonMatchesPattern($json, __DIR__.'/expected.detail.json');
    }
}
