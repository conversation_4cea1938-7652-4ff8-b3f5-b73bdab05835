<?php

declare(strict_types=1);

namespace App\Tests\Functional\SupplierPrices;

use App\Application\DataFixtures\Factory\Solution\SupplierPriceFactory;
use App\Domain\User\Roles;
use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class SupplierPricesGetTest extends ApiTestCase
{
    public function testAuthenticatedAuthorized(): void
    {
        $object = SupplierPriceFactory::createOne()->object();

        $this->loginAs(Roles::ROLE_ADMIN);

        $this->client->jsonRequest(Request::METHOD_GET, "backend/supplier_prices/{$object->getId()->toString()}");
        self::assertResponseIsSuccessful();
    }

    public function testAuthenticatedUnauthorized(): void
    {
        $object = SupplierPriceFactory::createOne()->object();

        $this->loginAs(Roles::ROLE_SUPPLIER);

        $this->client->jsonRequest(Request::METHOD_GET, "backend/supplier_prices/{$object->getId()->toString()}");
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function testUnauthenticated(): void
    {
        $object = SupplierPriceFactory::createOne()->object();

        $this->client->jsonRequest(Request::METHOD_GET, "backend/supplier_prices/{$object->getId()->toString()}");
        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }
}
