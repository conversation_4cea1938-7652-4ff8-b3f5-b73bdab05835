<?php

declare(strict_types=1);

namespace App\Tests\Functional\Files;

use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use <PERSON><PERSON><PERSON>ck\Browser\Json;

final class FilesPostTest extends ApiTestCase
{
    public function testCreateFile(): void
    {
        $file = new UploadedFile(
            __DIR__.'/the-perfect-day-out.jpg',
            'the-perfect-day-out.jpg',
            'image/jpg',
        );

        $response = static::createClient()->request(Request::METHOD_POST, '/backend/files.json', [
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'multipart/form-data',
            ],
            'extra' => [
                'files' => [
                    'file' => $file,
                ],
            ],
        ]);

        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        self::assertJsonMatchesPattern(new Json($response->getContent()), __DIR__.'/expected.detail.json');
    }

    public function testEmptyBody(): void
    {
        $this->browser()
            ->request(Request::METHOD_POST, '/backend/files.json', [
                'headers' => [
                    'Content-Type' => 'multipart/form-data',
                ],
                'body' => [],
            ])
            ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
