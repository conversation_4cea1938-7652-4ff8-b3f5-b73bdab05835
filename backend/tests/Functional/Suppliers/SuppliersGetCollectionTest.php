<?php

declare(strict_types=1);

namespace App\Tests\Functional\Suppliers;

use App\Application\DataFixtures\Factory\Contractor\SupplierFactory;
use App\Domain\User\Roles;
use App\Tests\Functional\ApiTestCase;

final class SuppliersGetCollectionTest extends ApiTestCase
{
    public function testEndpoint(): void
    {
        $this->loginAs(Roles::ROLE_ADMIN);

        SupplierFactory::createOne();

        $this->client->jsonRequest('GET', 'backend/suppliers.json');

        self::assertResponseIsSuccessful();
        $this->assertResponseMatchesFile(__DIR__.'/expected.collection.json');
    }
}
