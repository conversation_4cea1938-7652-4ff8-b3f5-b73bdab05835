<?php

declare(strict_types=1);

namespace App\Tests\Functional\IndoorUnits;

use App\Application\DataFixtures\Factory\Order\IndoorUnitFactory;
use App\Tests\Functional\ApiTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class IndoorUnitsGetTest extends ApiTestCase
{
    private const string URL = 'backend/indoor_units';

    public function testEndpoint(): void
    {
        $object = IndoorUnitFactory::createOne()->_real();

        $this->client->jsonRequest(Request::METHOD_GET, \sprintf('%s/%s.json', self::URL, $object->getId()));

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }
}
