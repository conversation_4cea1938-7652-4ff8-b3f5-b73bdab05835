import {
    DisciplineJsonldDisciplineRead,
    DisciplineJsonldDisciplineReadIdentifierEnum,
} from '@/api/generated';
import { AddressFields } from '@/components/form/AddressFields';
import {
    registerCompleteFormSchema,
    RegisterCompleteFormType,
} from '@/components/register/schema/RegisterCompleteFormSchema';
import { useRegisterCompleteStepsContext } from '@/context/RegisterCompleteStepsContext';
import {
    Box,
    Button,
    Checkbox,
    CheckboxGroup,
    Flex,
    Grid,
    GridItem,
    Heading,
    Input,
    VStack,
} from '@chakra-ui/react';
import {
    faArrowRight,
    faPaintRoller,
    faTrowelBricks,
} from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Trans } from '@lingui/macro';
import { FC } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import FormControlWithError from '../../form/FormControlWithError';

type Props = {
    disciplines: DisciplineJsonldDisciplineRead[];
};

const RegisterCompleteForm: FC<Props> = ({ disciplines }) => {
    const { saveAndNextStep } = useRegisterCompleteStepsContext();

    const methods = useForm<RegisterCompleteFormType>({
        resolver: zodResolver(registerCompleteFormSchema),
        defaultValues: {
            disciplines: [],
        },
    });

    const {
        register,
        handleSubmit,
        control,
        formState: { errors },
    } = methods;

    const onSubmit = async (data: RegisterCompleteFormType) => {
        saveAndNextStep(data);
    };

    const getIconForDiscipline = (
        discipline: DisciplineJsonldDisciplineReadIdentifierEnum
    ) => {
        switch (discipline) {
            case DisciplineJsonldDisciplineReadIdentifierEnum.Plasterer:
                return faTrowelBricks;
            case DisciplineJsonldDisciplineReadIdentifierEnum.Painter:
            default:
                return faPaintRoller;
        }
    };

    return (
        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} noValidate>
                <VStack alignItems={'stretch'} gap={'sm'} mb={'sm'}>
                    <FormControlWithError
                        error={errors.disciplines?.message}
                        isRequired
                        mb={0}
                    >
                        <Controller
                            name="disciplines"
                            control={control}
                            render={({ field }) => (
                                <CheckboxGroup
                                    value={field.value}
                                    onChange={field.onChange}
                                >
                                    <Grid
                                        gridTemplateColumns={'repeat(2, 1fr)'}
                                        gap={'sm'}
                                    >
                                        {disciplines.map((discipline) => (
                                            <GridItem key={discipline['@id']}>
                                                <Checkbox
                                                    value={discipline['@id']}
                                                    variant={'card'}
                                                    flexDir={'column-reverse'}
                                                >
                                                    {discipline.identifier && (
                                                        <Box mb={'xs'}>
                                                            <FontAwesomeIcon
                                                                icon={getIconForDiscipline(
                                                                    discipline.identifier
                                                                )}
                                                                size={'2xl'}
                                                            />
                                                        </Box>
                                                    )}
                                                    {discipline.name}
                                                </Checkbox>
                                            </GridItem>
                                        ))}
                                    </Grid>
                                </CheckboxGroup>
                            )}
                        />
                    </FormControlWithError>

                    <Heading size={'sm'}>
                        <Trans>Bedrijfsgegevens</Trans>
                    </Heading>

                    <FormControlWithError
                        error={errors.companyName?.message}
                        label={<Trans>Bedrijfsnaam</Trans>}
                        isRequired
                        mb={0}
                    >
                        <Input
                            {...register('companyName')}
                            autoComplete="organization"
                        />
                    </FormControlWithError>

                    <Flex flexDir={{ base: 'column', lg: 'row' }} gap={'sm'}>
                        <FormControlWithError
                            error={errors.cocNumber?.message}
                            label={<Trans>KvK nummer</Trans>}
                            mb={0}
                        >
                            <Input {...register('cocNumber')} />
                        </FormControlWithError>

                        <FormControlWithError
                            error={errors.vatNumber?.message}
                            label={<Trans>Btw nummer</Trans>}
                            mb={0}
                        >
                            <Input {...register('vatNumber')} />
                        </FormControlWithError>
                    </Flex>

                    <Flex flexDir={{ base: 'column', lg: 'row' }} gap={'sm'}>
                        <FormControlWithError
                            error={errors.iban?.message}
                            label={<Trans>IBAN</Trans>}
                            mb={0}
                        >
                            <Input {...register('iban')} />
                        </FormControlWithError>

                        <FormControlWithError
                            error={errors.ibanName?.message}
                            label={<Trans>IBAN tenaamstelling</Trans>}
                            mb={0}
                        >
                            <Input {...register('ibanName')} />
                        </FormControlWithError>
                    </Flex>
                </VStack>

                <VStack alignItems={'stretch'} gap={'sm'} mb={'sm'}>
                    <Heading size={'sm'}>
                        <Trans>Contactgegevens</Trans>
                    </Heading>

                    <AddressFields />

                    <FormControlWithError
                        error={errors.contactEmail?.message}
                        label={<Trans>Zakelijk e-mailadres</Trans>}
                        isRequired
                        mb={0}
                    >
                        <Input
                            {...register('contactEmail')}
                            type="email"
                            autoComplete="email"
                        />
                    </FormControlWithError>

                    <FormControlWithError
                        error={errors.contactPhoneNumber?.message}
                        label={<Trans>Zakelijk telefoonnummer</Trans>}
                        isRequired
                        mb={0}
                    >
                        <Input
                            {...register('contactPhoneNumber')}
                            type="tel"
                            autoComplete="tel-local"
                            inputMode="numeric"
                            defaultValue={'+31'}
                        />
                    </FormControlWithError>
                </VStack>

                <Flex justify={'flex-end'}>
                    <Button
                        type="submit"
                        rightIcon={<FontAwesomeIcon icon={faArrowRight} />}
                    >
                        <Trans>Volgende</Trans>
                    </Button>
                </Flex>
            </form>
        </FormProvider>
    );
};

export default RegisterCompleteForm;
