import { zImageUploadSchema, zStringRequired } from '@/constants/form';
import { i18n } from '@lingui/core';
import { z } from 'zod';

export const elementsPriceIndicationFormSchema = z.object({
    localities: z
        .record(
            z.object({
                elementGroups: z
                    .record(
                        z.object({
                            elements: z.record(
                                z.object({
                                    quantity: z.number().optional(),
                                })
                            ),
                            isInactive: z.boolean().optional(),
                        })
                    )
                    .refine((elementGroups) => {
                        return Object.values(elementGroups)
                            .flatMap((elementGroup) =>
                                Object.values(elementGroup.elements)
                            )
                            .some((element) => {
                                return (
                                    element.quantity !== undefined &&
                                    element.quantity > 0
                                );
                            });
                    }, i18n._(`Selecteer minimaal 1 element`)),
            })
        )
        .refine(
            (data) => Object.keys(data).length > 0,
            i18n._(`Selecteer minimaal 1 element per onderdeel`)
        ),
});

export type ElementsStepPriceIndicationFormType = z.infer<
    typeof elementsPriceIndicationFormSchema
>;

export const photoLocalityUploadPriceIndicationFormSchema = z.object({
    localitiesPhotos: z
        .array(
            z
                .object({
                    localityId: zStringRequired,
                    description: z
                        .string()
                        .trim()
                        .max(
                            255,
                            i18n._(
                                `Omschrijving is te lang, maximaal 255 tekens`
                            )
                        )
                        .optional(),
                })
                .merge(zImageUploadSchema)
        )
        .min(1, i18n._(`Voeg minimaal één foto toe`)),
});

export type PhotoUploadStepPriceIndicationFormType = z.infer<
    typeof photoLocalityUploadPriceIndicationFormSchema
>;
