import { ContactPutFormType } from '@/components/contactEdit/schema/contactPutFormSchema';
import FormControlWithError from '@/components/form/FormControlWithError';
import { Flex, Heading, Input, Textarea, VStack } from '@chakra-ui/react';
import { t, Trans } from '@lingui/macro';
import { FC } from 'react';
import { useFormContext } from 'react-hook-form';

type Props = {
    isLoading?: boolean;
};

export const ContactDetailsFields: FC<Props> = ({ isLoading }) => {
    const {
        register,
        formState: { errors },
    } = useFormContext<ContactPutFormType>();

    return (
        <VStack alignItems={'stretch'} gap={'sm'}>
            <Heading size={'sm'}>
                <Trans>Contactgegevens</Trans>
            </Heading>
            <Flex
                alignItems={'fle-start'}
                gap={'sm'}
                flexDir={{ base: 'column', md: 'row' }}
            >
                <FormControlWithError
                    label={t`Voornaam`}
                    error={errors.firstName?.message}
                    isRequired
                    isDisabled={isLoading}
                    mb={0}
                >
                    <Input {...register('firstName')} />
                </FormControlWithError>

                <FormControlWithError
                    label={t`Achternaam`}
                    error={errors.lastName?.message}
                    isRequired
                    isDisabled={isLoading}
                    mb={0}
                >
                    <Input {...register('lastName')} />
                </FormControlWithError>
            </Flex>

            <FormControlWithError
                label={t`Telefoonnummer`}
                error={errors.phoneNumber?.message}
                isRequired
                isDisabled={isLoading}
                mb={0}
            >
                <Input
                    type="tel"
                    autoComplete="tel-local"
                    inputMode="numeric"
                    {...register('phoneNumber')}
                />
            </FormControlWithError>
            <FormControlWithError
                label={t`E-mailadres`}
                error={errors.emailAddress?.message}
                isRequired
                isDisabled={isLoading}
                mb={0}
            >
                <Input
                    type="email"
                    autoComplete="email"
                    {...register('emailAddress')}
                />
            </FormControlWithError>

            <Heading size={'sm'}>
                <Trans>Extra info</Trans>
            </Heading>
            <FormControlWithError
                error={errors.comments?.message}
                isRequired
                isDisabled={isLoading}
                mb={0}
                floatingLabel={false}
            >
                <Textarea
                    {...register('comments')}
                    rows={4}
                    placeholder={t`Opmerking`}
                    variant={'outline'}
                />
            </FormControlWithError>
        </VStack>
    );
};
