import { Box, Skeleton, Text, VStack } from '@chakra-ui/react';
import { FC } from 'react';

export const PropertyListLoader: FC = () => {
    const amountOfSkeletonLoaders = 2;

    return new Array(amountOfSkeletonLoaders).fill('').map((_, index) => (
        <Skeleton key={index} width={'full'} borderRadius={'lg'}>
            <VStack
                p={'sm'}
                borderWidth={1}
                alignItems={'stretch'}
                gap={'xs'}
                height={'full'}
            >
                <Box>
                    <Text>&nbsp;</Text>
                    <Text>&nbsp;</Text>
                </Box>
                <Text>&nbsp;</Text>
            </VStack>
        </Skeleton>
    ));
};
