import { QuotationSendModal } from '@/components/quotationSendModal/shared/QuotationSendModal';
import { usePutPlastererQuotationSend } from '@/hooks/plastererQuotation/usePutPlastererQuotationSend';
import { useToastPrimitive } from '@/hooks/useToastPrimitive';
import { createQuotationDetailPath } from '@/utilities/routes';
import { Trans } from '@lingui/macro';
import { useRouter } from 'next/router';
import { FC } from 'react';

type Props = {
    isOpen: boolean;
    onClose: () => void;
    quotationId: string;
};

export const QuotationSendModalInitializer: FC<Props> = ({
    isOpen,
    onClose,
    quotationId,
}) => {
    const { replace } = useRouter();
    const { mutateAsync, isPending: isPendingMutate } =
        usePutPlastererQuotationSend();
    const { showToast } = useToastPrimitive();

    const onSend = async () => {
        try {
            await mutateAsync({
                id: quotationId,
                payload: { quotationId },
            });

            onClose();

            replace(createQuotationDetailPath(quotationId));

            showToast({
                title: <Trans>Offerte verstuurd</Trans>,
            });
        } catch (error) {
            showToast({
                status: 'error',
                title: <Trans>Er ging iets mis</Trans>,
                description: <Trans>Probeer het later opnieuw</Trans>,
            });
        }
    };

    return (
        <QuotationSendModal
            isOpen={isOpen}
            onClose={onClose}
            onSend={onSend}
            isPending={isPendingMutate}
        />
    );
};
