import { QuotationSummaryDetailLine } from '@/components/quotationSummary/shared/QuotationSummaryDetailLine';
import { useGetMaterialUnit } from '@/hooks/materialUnit/useGetMaterialUnit';
import { iriToId } from '@/utilities/iri';
import { FC } from 'react';

type Props = {
    label: string;
    quantity: number;
    materialUnit: string | undefined;
};

export const QuotationSummaryMaterialLine: FC<Props> = ({
    label,
    quantity,
    materialUnit,
}) => {
    const { data } = useGetMaterialUnit({
        id: materialUnit ? iriToId(materialUnit) : undefined,
    });

    const materialName = data?.shortName ?? '';

    return (
        <QuotationSummaryDetailLine
            label={label}
            quantity={quantity}
            unit={materialName}
        />
    );
};
