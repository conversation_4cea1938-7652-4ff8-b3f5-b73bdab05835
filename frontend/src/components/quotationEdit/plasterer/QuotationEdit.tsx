import {
    PlastererQuotationJsonldOpenApiRead,
    PlastererQuotationJsonldOpenApiReadStatusEnum,
    PlastererSpecificationJsonldSpecificationRead,
} from '@/api/generated';
import { QuotationSpecificationForm } from '@/components/quotationCreate/plasterer/forms/QuotationSpecificationForm';
import { QuotationSpecificationPutFormFields } from '@/components/quotationDetail/plasterer/QuotationSpecificationPutFormFields';
import {
    quotationSpecificationPutFormSchema,
    QuotationSpecificationPutFormType,
} from '@/components/quotationDetail/plasterer/schema/quotationSchema';
import { QuotationSummaryBarInitializer } from '@/components/quotationSummary/plasterer/QuotationSummaryBarInitializer';
import { usePutPlastererSpecification } from '@/hooks/plastererSpecification/usePutPlastererSpecification';
import { useDebouncedCallback } from '@/hooks/useDebouncedCallback';
import { useToastPrimitive } from '@/hooks/useToastPrimitive';
import { ContainerSize } from '@/utilities/container';
import {
    mapPlastererSpecificationToPutFormFields,
    plastererSpecificationPutFormDto,
} from '@/utilities/plastererSpecification';
import {
    createQuotationConceptDetailPath,
    createQuotationDetailPath,
} from '@/utilities/routes';
import { Button, Container, Heading, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { t, Trans } from '@lingui/macro';
import { useRouter } from 'next/router';
import { FC, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

type Props = {
    quotation: PlastererQuotationJsonldOpenApiRead;
    specification: PlastererSpecificationJsonldSpecificationRead;
};

export const QuotationEdit: FC<Props> = ({ quotation, specification }) => {
    const { push } = useRouter();
    const {
        mutateAsync: mutateAsyncPutPlastererSpecification,
        isPending: isPendingPutPlastererSpecification,
    } = usePutPlastererSpecification();
    const { showToast } = useToastPrimitive();

    const methods = useForm<QuotationSpecificationPutFormType>({
        resolver: zodResolver(quotationSpecificationPutFormSchema),
    });

    const { handleSubmit, reset, trigger, getValues } = methods;

    useEffect(() => {
        reset(
            mapPlastererSpecificationToPutFormFields(specification, quotation),
            {
                keepDirtyValues: true,
            }
        );
    }, [specification, quotation, reset]);

    const onSubmit = async (data: QuotationSpecificationPutFormType) => {
        try {
            await saveFormData(data);

            showToast({
                title: <Trans>Offerte opgeslagen</Trans>,
            });

            if (quotation.id) {
                if (
                    quotation.status ===
                    PlastererQuotationJsonldOpenApiReadStatusEnum.Draft
                ) {
                    return push(createQuotationConceptDetailPath(quotation.id));
                }

                return push(createQuotationDetailPath(quotation.id));
            }
        } catch (error) {
            // do nothing
        }
    };

    const saveFormData = async (data: QuotationSpecificationPutFormType) => {
        if (!specification.id || !quotation.id) {
            return null;
        }

        try {
            const payload = plastererSpecificationPutFormDto(
                data,
                specification
            );

            await mutateAsyncPutPlastererSpecification({
                id: specification.id,
                payload,
            });
        } catch (e) {
            showToast({
                status: 'error',
                title: <Trans>Er ging iets mis</Trans>,
                description: <Trans>Probeer het later opnieuw</Trans>,
            });
        }
    };

    const autoSave = async (
        values?: Partial<QuotationSpecificationPutFormType>
    ) => {
        const data = getValues();
        const isValid = await trigger();

        const mergedData = { ...data, ...values };

        if (!isValid) {
            return;
        }

        try {
            await saveFormData(mergedData);
        } catch (error) {
            // do nothing
        }
    };

    const debouncedAutoSave = useDebouncedCallback(
        (values?: Partial<QuotationSpecificationPutFormType>) => {
            autoSave(values);
        }
    );

    return (
        <FormProvider {...methods}>
            <Container maxW={ContainerSize.LG} p={'md'}>
                <form onSubmit={handleSubmit(onSubmit)} noValidate>
                    <VStack alignItems={'stretch'} gap={'md'}>
                        <Heading>{t`${quotation.number} bijwerken`}</Heading>

                        <VStack
                            alignItems={'stretch'}
                            bgColor={'white'}
                            p={'md'}
                            borderRadius={'2xl'}
                            gap={'md'}
                        >
                            <Heading size={'lg'}>
                                <Trans>Ruimtes</Trans>
                            </Heading>
                            <QuotationSpecificationForm
                                specification={specification}
                            />

                            <Heading size={'lg'}>
                                <Trans>Overig</Trans>
                            </Heading>

                            <QuotationSpecificationPutFormFields
                                specification={specification}
                                autoSave={debouncedAutoSave}
                            />

                            <Button
                                type={'submit'}
                                alignSelf={'flex-end'}
                                isLoading={isPendingPutPlastererSpecification}
                            >
                                <Trans>Opslaan</Trans>
                            </Button>
                        </VStack>
                    </VStack>
                </form>
            </Container>

            <QuotationSummaryBarInitializer
                specification={specification}
                position={'sticky'}
                bottom={0}
            />
        </FormProvider>
    );
};
