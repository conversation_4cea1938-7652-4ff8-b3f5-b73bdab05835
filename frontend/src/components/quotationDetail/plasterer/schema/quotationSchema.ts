import { PlastererSpecificationUpdatePlastererSpecificationCommandJsonldVatPercentageEnum } from '@/api/generated';
import {
    zNumberPositiveOptional,
    zNumberPositiveRequired,
    zStringOptional,
    zStringRequired,
} from '@/constants/form';
import { z } from 'zod';

export const quotationSpecificationExtraSchema = z.object({
    specificationExtraId: zStringOptional,
    vatPercentage: z.nativeEnum(
        PlastererSpecificationUpdatePlastererSpecificationCommandJsonldVatPercentageEnum
    ),
    name: zStringRequired,
    provisional: z.boolean(),
    includeInMaintenance: z.boolean().optional(),
    price: zNumberPositiveRequired,
});

export type QuotationSpecificationExtraFormType = z.infer<
    typeof quotationSpecificationExtraSchema
>;

export const quotationSpecificationPutFormSchema = z.object({
    vatPercentage: z.nativeEnum(
        PlastererSpecificationUpdatePlastererSpecificationCommandJsonldVatPercentageEnum
    ),
    hours: zNumberPositiveOptional,
    materialPrice: zNumberPositiveOptional,
    discountPercentage: zNumberPositiveOptional,
    quotationDescription: zStringOptional,
    quotationExclusion: zStringOptional,
});

export type QuotationSpecificationPutFormType = z.infer<
    typeof quotationSpecificationPutFormSchema
>;
