import { SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum } from '@/api/generated';
import {
    QuotationSpecificationExtraFormType,
    quotationSpecificationExtraSchema,
} from '@/components/quotationDetail/painter/schema/quotationSchema';
import { QuotationSpecificationExtrasModal } from '@/components/quotationDetail/shared/QuotationSpecificationExtrasModal';
import { QueryKeys } from '@/constants/query';
import { usePostSpecificationExtra } from '@/hooks/specificationExtra/usePostSpecificationExtra';
import { useToastPrimitive } from '@/hooks/useToastPrimitive';
import { iriToId } from '@/utilities/iri';
import { specificationExtraPostFormDto } from '@/utilities/specificationExtra';
import { zodResolver } from '@hookform/resolvers/zod';
import { i18n } from '@lingui/core';
import { Trans } from '@lingui/macro';
import { useQueryClient } from '@tanstack/react-query';
import { FC } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

type Props = {
    isOpen: boolean;
    onClose: () => void;
    specificationIri: string;
    specificationMaintenancePlanIri: string | undefined;
};

export const quotationSpecificationExtrasVatOptions = [
    {
        label: i18n._('9% BTW'),
        value: SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum.NUMBER_9.toString(),
    },
    {
        label: i18n._('21% BTW'),
        value: SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum.NUMBER_21.toString(),
    },
];

export const QuotationSpecificationExtrasModalInitializer: FC<Props> = ({
    isOpen,
    onClose,
    specificationIri,
    specificationMaintenancePlanIri,
}) => {
    const {
        mutateAsync: mutateAsyncSpecificationExtra,
        isPending: isPendingExtra,
    } = usePostSpecificationExtra();
    const { showToast } = useToastPrimitive();
    const queryClient = useQueryClient();

    const methods = useForm<QuotationSpecificationExtraFormType>({
        resolver: zodResolver(quotationSpecificationExtraSchema),
    });

    const { reset } = methods;

    const onSubmit = async (data: QuotationSpecificationExtraFormType) => {
        try {
            await mutateAsyncSpecificationExtra(
                specificationExtraPostFormDto(data, specificationIri)
            );

            queryClient.invalidateQueries({
                queryKey: [
                    QueryKeys.Specification,
                    { id: iriToId(specificationIri) },
                ],
            });

            queryClient.invalidateQueries({
                queryKey: [
                    QueryKeys.SpecificationMaintenanceJobs,
                    {
                        specificationMaintenancePlan:
                            specificationMaintenancePlanIri,
                    },
                ],
            });
        } catch (e) {
            showToast({
                status: 'error',
                title: <Trans>Er ging iets mis</Trans>,
                description: <Trans>Probeer het later opnieuw</Trans>,
            });
        }

        onClose();
        reset();
    };

    return (
        <FormProvider {...methods}>
            <QuotationSpecificationExtrasModal
                isOpen={isOpen}
                onClose={onClose}
                onSubmit={onSubmit}
                isPending={isPendingExtra}
                vatOptions={quotationSpecificationExtrasVatOptions}
                hasMaintenancePlan={!!specificationMaintenancePlanIri}
            />
        </FormProvider>
    );
};
