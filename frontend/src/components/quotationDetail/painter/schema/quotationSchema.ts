import { SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum } from '@/api/generated';
import { maintenancePlanPutSchema } from '@/components/maintenancePlan/schema/maintenancePlanSchema';
import {
    zNumberPositiveOptional,
    zNumberPositiveRequired,
    zStringOptional,
    zStringRequired,
} from '@/constants/form';
import { z } from 'zod';

export const quotationSpecificationExtraSchema = z.object({
    specificationExtraId: zStringOptional,
    vatPercentage: z.nativeEnum(
        SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum
    ),
    name: zStringRequired,
    provisional: z.boolean(),
    includeInMaintenance: z.boolean().optional(),
    price: zNumberPositiveRequired,
});

export type QuotationSpecificationExtraFormType = z.infer<
    typeof quotationSpecificationExtraSchema
>;

export const quotationSpecificationPutFormSchema = z.object({
    vatPercentage: z.nativeEnum(
        SpecificationUpdateSpecificationCommandJsonldVatPercentageEnum
    ),
    hours: zNumberPositiveOptional,
    materialPrice: zNumberPositiveOptional,
    discountPercentage: zNumberPositiveOptional,
    quotationDescription: zStringOptional,
    quotationExclusion: zStringOptional,
    offerMaintenancePlan: z.boolean(),
});

export type QuotationSpecificationPutFormType = z.infer<
    typeof quotationSpecificationPutFormSchema
>;

export const quotationAndMaintenancePlanSchema =
    quotationSpecificationPutFormSchema.merge(maintenancePlanPutSchema);

export type QuotationAndMaintenancePlanFormType = z.infer<
    typeof quotationAndMaintenancePlanSchema
>;
