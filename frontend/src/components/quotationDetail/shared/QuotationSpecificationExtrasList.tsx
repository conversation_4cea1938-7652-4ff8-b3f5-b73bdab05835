import {
    PlastererSpecificationExtraJsonldSpecificationRead,
    SpecificationExtraJsonldSpecificationRead,
} from '@/api/generated';
import { formatMoney } from '@/utilities/string';
import {
    Badge,
    Box,
    Heading,
    HStack,
    IconButton,
    Text,
    VStack,
} from '@chakra-ui/react';
import { faCalendar, faTrashAlt } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { t, Trans } from '@lingui/macro';
import { FC } from 'react';

type Props = {
    specificationExtras:
        | SpecificationExtraJsonldSpecificationRead[]
        | PlastererSpecificationExtraJsonldSpecificationRead[];
    onRemove: (id: string) => void;
    vatOptions: { label: string; value: string }[];
    isPending?: boolean;
};

export const QuotationSpecificationExtrasList: FC<Props> = ({
    specificationExtras,
    onRemove,
    vatOptions,
    isPending,
}) => {
    return (
        <VStack
            alignItems={'stretch'}
            gap={0}
            borderTopWidth={1}
            borderBottomWidth={1}
        >
            {specificationExtras.map((specificationExtra) => (
                <HStack
                    key={specificationExtra.id}
                    py={'sm'}
                    className="quotation-specification-extra-item"
                    sx={{
                        '+ .quotation-specification-extra-item': {
                            borderTopWidth: 1,
                        },
                    }}
                >
                    <Box flex={1}>
                        <HStack alignItems={'baseline'}>
                            <Heading size={'sm'} flex={1}>
                                {specificationExtra.name}
                            </Heading>
                            {specificationExtra.price && (
                                <Text>
                                    {formatMoney(specificationExtra.price)}
                                </Text>
                            )}
                        </HStack>
                        <HStack alignItems={'baseline'}>
                            <Text color={'gray.500'} flex={1}>
                                {vatOptions.find(
                                    (option) =>
                                        parseInt(option.value) ===
                                        specificationExtra.vatPercentage
                                )?.label ?? specificationExtra.vatPercentage}
                            </Text>
                            {specificationExtra.provisional && (
                                <Text color={'gray.500'}>
                                    <Trans>Verrekenbaar</Trans>
                                </Text>
                            )}
                            {'includeInMaintenance' in specificationExtra &&
                                specificationExtra.includeInMaintenance && (
                                    <Badge
                                        display={'inline-flex'}
                                        gap={'xs'}
                                        alignItems={'center'}
                                        color={'gray.600'}
                                        backgroundColor={'gray.300'}
                                        variant="solid"
                                    >
                                        <FontAwesomeIcon icon={faCalendar} />
                                        <Trans>MJOP</Trans>
                                    </Badge>
                                )}
                        </HStack>
                    </Box>

                    <IconButton
                        onClick={() =>
                            onRemove(specificationExtra.id as string)
                        }
                        icon={<FontAwesomeIcon icon={faTrashAlt} />}
                        aria-label={t`Toeslag verwijderen`}
                        variant={'ghost'}
                        colorScheme={'gray'}
                        size={'sm'}
                        isLoading={isPending}
                    />
                </HStack>
            ))}
        </VStack>
    );
};
