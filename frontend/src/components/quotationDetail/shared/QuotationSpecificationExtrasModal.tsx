import FormControlWithError from '@/components/form/FormControlWithError';
import { QuotationSpecificationExtraFormType as PainterQuotationSpecificationExtraFormType } from '@/components/quotationDetail/painter/schema/quotationSchema';
import { QuotationSpecificationExtraFormType as PlastererQuotationSpecificationExtraFormType } from '@/components/quotationDetail/plasterer/schema/quotationSchema';
import {
    Button,
    Checkbox,
    Heading,
    HStack,
    Input,
    InputGroup,
    InputLeftAddon,
    Modal,
    ModalBody,
    ModalCloseButton,
    ModalContent,
    ModalHeader,
    ModalOverlay,
    Radio,
    RadioGroup,
    Text,
    VStack,
} from '@chakra-ui/react';
import { Trans } from '@lingui/macro';
import { FC, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

type Props = {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (
        data:
            | PainterQuotationSpecificationExtraFormType
            | PlastererQuotationSpecificationExtraFormType
    ) => void;
    vatOptions: { label: string; value: string }[];
    isPending?: boolean;
    hasMaintenancePlan?: boolean;
};

export const QuotationSpecificationExtrasModal: FC<Props> = ({
    isOpen,
    onClose,
    onSubmit,
    isPending,
    vatOptions,
    hasMaintenancePlan,
}) => {
    const {
        register,
        formState: { errors },
        handleSubmit,
        control,
        watch,
        setValue,
    } = useFormContext<
        | PainterQuotationSpecificationExtraFormType
        | PlastererQuotationSpecificationExtraFormType
    >();

    const provisionalValue = watch('provisional');

    useEffect(() => {
        // includeInMaintenance is not allowed when provisional is true
        if (provisionalValue) {
            setValue('includeInMaintenance', false);
        }
    }, [provisionalValue, setValue]);

    return (
        <Modal isOpen={isOpen} onClose={onClose} size={'xl'} isCentered>
            <ModalOverlay />
            <ModalContent>
                <ModalHeader>
                    <ModalCloseButton />
                </ModalHeader>
                <ModalBody>
                    <form
                        onSubmit={(e) => {
                            e.stopPropagation(); // don't submit parent form
                            handleSubmit(onSubmit)(e);
                        }}
                        noValidate
                    >
                        <VStack alignItems={'stretch'} gap={'sm'}>
                            <Heading size={'md'} flex={1}>
                                <Trans>Toeslag toevoegen</Trans>
                            </Heading>
                            <Text>
                                <Trans>
                                    Hieronder zie je de details van de
                                    berekening.
                                </Trans>
                            </Text>

                            <FormControlWithError
                                error={errors.name?.message}
                                label={<Trans>Naam toeslag</Trans>}
                                helpText={
                                    <Trans>
                                        Bijvoorbeeld voorrijkosten, hoogwerker
                                        etc.
                                    </Trans>
                                }
                                isDisabled={isPending}
                                mb={0}
                            >
                                <Input {...register('name')} />
                            </FormControlWithError>

                            <FormControlWithError
                                error={errors.price?.message}
                                floatingLabel={false}
                                isDisabled={isPending}
                                mb={0}
                            >
                                <InputGroup variant={'outline'}>
                                    <InputLeftAddon>€</InputLeftAddon>
                                    <Input
                                        type="number"
                                        inputMode="decimal"
                                        min={0}
                                        {...register('price', {
                                            valueAsNumber: true,
                                        })}
                                    />
                                </InputGroup>
                            </FormControlWithError>

                            <FormControlWithError
                                error={errors.vatPercentage?.message}
                                floatingLabel={false}
                                isDisabled={isPending}
                                mb={0}
                            >
                                <Controller
                                    name={'vatPercentage'}
                                    control={control}
                                    render={({
                                        field: {
                                            onChange,
                                            value,
                                            ...fieldRest
                                        },
                                    }) => (
                                        <RadioGroup
                                            {...fieldRest}
                                            onChange={(val) => {
                                                onChange(
                                                    val ? parseInt(val) : null
                                                );
                                            }}
                                            value={
                                                value ? value.toString() : ''
                                            }
                                        >
                                            <HStack
                                                alignItems={'flex-start'}
                                                spacing={'sm'}
                                            >
                                                {vatOptions.map((option) => (
                                                    <Radio
                                                        key={option.label}
                                                        value={option.value}
                                                    >
                                                        {option.label}
                                                    </Radio>
                                                ))}
                                            </HStack>
                                        </RadioGroup>
                                    )}
                                />
                            </FormControlWithError>

                            <FormControlWithError
                                error={errors.provisional?.message}
                                isDisabled={isPending}
                                mb={0}
                            >
                                <Checkbox
                                    {...register('provisional')}
                                    colorScheme="blue"
                                >
                                    <Text>
                                        <Trans>
                                            Dit is een verrekenbare toeslag
                                        </Trans>
                                    </Text>
                                </Checkbox>
                            </FormControlWithError>

                            {hasMaintenancePlan && (
                                <FormControlWithError
                                    error={errors.includeInMaintenance?.message}
                                    isDisabled={isPending || provisionalValue}
                                    mb={0}
                                >
                                    <Controller
                                        name={'includeInMaintenance'}
                                        control={control}
                                        render={({
                                            field: { value, ...fieldRest },
                                        }) => (
                                            <Checkbox
                                                {...fieldRest}
                                                isChecked={value}
                                                colorScheme="blue"
                                            >
                                                <Text>
                                                    <Trans>
                                                        Meenemen in MJOP
                                                    </Trans>
                                                </Text>
                                            </Checkbox>
                                        )}
                                    />
                                </FormControlWithError>
                            )}

                            <Button type={'submit'} isLoading={isPending}>
                                <Trans>Toeslag toevoegen</Trans>
                            </Button>
                        </VStack>
                    </form>
                </ModalBody>
            </ModalContent>
        </Modal>
    );
};
