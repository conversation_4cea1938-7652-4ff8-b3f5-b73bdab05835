import {
    PlastererSpecificationJsonldSpecificationRead,
    SpecificationJsonldSpecificationRead,
} from '@/api/generated';
import { QuotationExtraLineItem } from '@/components/quotationDetail/shared/QuotationExtraLineItem';
import { formatMoney } from '@/utilities/string';
import { Box, Heading, VStack } from '@chakra-ui/react';
import { t, Trans } from '@lingui/macro';
import { FC } from 'react';

type Props = {
    specification:
        | SpecificationJsonldSpecificationRead
        | PlastererSpecificationJsonldSpecificationRead;
};

export const QuotationExtrasList: FC<Props> = ({ specification }) => {
    const hasExtras =
        !!specification.specificationExtras?.length ||
        specification.materialPrice ||
        specification.hours ||
        specification.discountPercentage ||
        specification.quotationDescription ||
        specification.quotationExclusion;

    if (!hasExtras) {
        return null;
    }

    return (
        <Box>
            <Heading
                size={'xs'}
                color={'gray.500'}
                textTransform={'uppercase'}
                fontWeight={'normal'}
                mb={'xs'}
            >
                <Trans>Overig</Trans>
            </Heading>

            <VStack
                bgColor={'white'}
                borderWidth={1}
                borderRadius={'2xl'}
                p={'md'}
                alignItems={'stretch'}
                gap={'sm'}
            >
                {!!specification.specificationExtras?.length && (
                    <VStack gap={'xs'} alignItems={'stretch'}>
                        <Heading size={'sm'} flex={1}>
                            <Trans>Toeslagen</Trans>
                        </Heading>

                        {specification.specificationExtras.map((extra) => (
                            <QuotationExtraLineItem
                                label={extra.name ?? ''}
                                subLabel={
                                    extra.provisional
                                        ? t`verrekenbaar`
                                        : 'includeInMaintenance' in extra &&
                                          extra.includeInMaintenance
                                        ? t`MJOP`
                                        : undefined
                                }
                                value={formatMoney(
                                    extra.price ?? {
                                        amount: '0',
                                    }
                                )}
                                key={extra.id}
                            />
                        ))}
                    </VStack>
                )}

                {(specification.hours || specification.materialPrice) && (
                    <VStack gap={'xs'} alignItems={'stretch'}>
                        <Heading size={'sm'} flex={1}>
                            <Trans>Arbeidsuur en materiaal</Trans>
                        </Heading>
                        {specification.hours && (
                            <QuotationExtraLineItem
                                label={t`Arbeidsuren`}
                                value={`${specification.hours}\u00A0uur`}
                            />
                        )}
                        {specification.materialPrice && (
                            <QuotationExtraLineItem
                                label={t`Materiaal`}
                                value={formatMoney(specification.materialPrice)}
                            />
                        )}
                    </VStack>
                )}

                {specification.discountPercentage && (
                    <VStack gap={'xs'} alignItems={'stretch'}>
                        <Heading size={'sm'} flex={1}>
                            <Trans>Korting</Trans>
                        </Heading>
                        <QuotationExtraLineItem
                            label={t`Korting`}
                            value={`${specification.discountPercentage}%`}
                        />
                    </VStack>
                )}

                {specification.quotationDescription && (
                    <VStack gap={'xs'} alignItems={'stretch'}>
                        <Heading size={'sm'} flex={1}>
                            <Trans>Opmerking op de offerte</Trans>
                        </Heading>
                        <QuotationExtraLineItem
                            label={specification.quotationDescription}
                        />
                    </VStack>
                )}

                {specification.quotationExclusion && (
                    <VStack gap={'xs'} alignItems={'stretch'}>
                        <Heading size={'sm'} flex={1}>
                            <Trans>Uitsluitingen op de offerte</Trans>
                        </Heading>
                        <QuotationExtraLineItem
                            label={specification.quotationExclusion}
                        />
                    </VStack>
                )}
            </VStack>
        </Box>
    );
};
