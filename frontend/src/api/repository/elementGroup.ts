import { elementGroupApi } from '@/api/client';
import { PagedProps } from '@/api/repository/misc';

type Props = {
    localities?: string[];
} & PagedProps;

export const getElementGroups = async ({
    localities,
    page = 1,
    itemsPerPage = 100,
}: Props) => {
    const { data } = await elementGroupApi.apiElementGroupsGetCollection({
        page,
        itemsPerPage,
        localities2: localities,
    });

    return data['hydra:member'];
};

export const getElementGroup = async (id: string) => {
    const { data } = await elementGroupApi.apiElementGroupsIdGet({ id });

    return data;
};
