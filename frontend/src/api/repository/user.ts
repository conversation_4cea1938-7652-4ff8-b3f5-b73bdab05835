import { userApiClient } from '@/api/client';
import {
    UserCreateUserCommandJsonld,
    UserResetPasswordRequestCommandJsonld,
} from '@/api/generated';

export const getUserMe = async () => {
    const { data } = await userApiClient.apiUsersmeGet();

    return data;
};

export const postUser = async (payload: UserCreateUserCommandJsonld) => {
    const { data } = await userApiClient.apiUsersPost({
        userCreateUserCommandJsonld: payload,
    });

    return data;
};

export const postResetPassword = async (
    payload: UserResetPasswordRequestCommandJsonld
) => {
    const { data } = await userApiClient.apiUsersresetPasswordPost({
        userResetPasswordRequestCommandJsonld: payload,
    });

    return data;
};

export const postResendVerificationMail = async (email: string) => {
    const { data } = await userApiClient.apiUsersresendVerificationEmailPost({
        userResendVerificationEmailCommandJsonld: {
            email,
        },
    });

    return data;
};
