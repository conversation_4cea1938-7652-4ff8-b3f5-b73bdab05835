import { SpecificationExtraCreateSpecificationExtraCommandJsonld } from '@/api/generated';
import { QuotationSpecificationExtraFormType } from '@/components/quotationDetail/painter/schema/quotationSchema';
import { floatToMoney } from '@/utilities/money';

export const specificationExtraPostFormDto = (
    data: QuotationSpecificationExtraFormType,
    specificationId: string
): Required<SpecificationExtraCreateSpecificationExtraCommandJsonld> => {
    return {
        specification: specificationId,
        price: floatToMoney(data.price ?? 0),
        name: data.name,
        provisional: data.provisional,
        vatPercentage: data.vatPercentage,
        includeInMaintenance: data.includeInMaintenance ?? false,
    };
};
