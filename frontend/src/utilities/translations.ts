import { i18n, Messages } from '@lingui/core';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

export async function loadCatalog(locale: string) {
    const { messages } = await import(`src/translations/locales/${locale}.po`);

    return messages;
}

export function useLinguiInit(messages: Messages) {
    const { locale, defaultLocale } = useRouter();
    const currentLocale = locale || defaultLocale!;
    const isClient = typeof window !== 'undefined';

    if (!isClient && currentLocale !== i18n.locale) {
        // there is single instance of i18n on the server
        i18n.loadAndActivate({ locale: currentLocale, messages });
    }
    if (isClient && !i18n.locale) {
        // first client render
        i18n.loadAndActivate({ locale: currentLocale, messages });
    }

    useEffect(() => {
        const localeDidChange = currentLocale !== i18n.locale;

        if (localeDidChange) {
            i18n.loadAndActivate({ locale: currentLocale, messages });
        }
    }, [currentLocale, messages]);

    return i18n;
}
