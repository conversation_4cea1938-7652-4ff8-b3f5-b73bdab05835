import { ContactCreate } from '@/components/contactCreate/ContactCreate';
import Layout from '@/components/layout/Layout';
import { MainSectionLayout } from '@/components/layout/MainSectionLayout';
import { ROUTES } from '@/constants/routes';
import { NextPageWithLayout } from '@/pages/_app';
import { loadCatalog } from '@/utilities/translations';
import { faAddressBook, faUserPlus } from '@fortawesome/pro-light-svg-icons';
import { t } from '@lingui/macro';
import { GetStaticProps } from 'next';

const ContactCreatePage: NextPageWithLayout = () => {
    return (
        <MainSectionLayout
            breadcrumbs={[
                {
                    label: t`Klanten`,
                    href: ROUTES.ContactsOverview,
                    icon: faAddressBook,
                },
                {
                    label: t`Nieuw`,
                    href: ROUTES.ContactsCreate,
                    icon: faUserPlus,
                },
            ]}
        >
            <ContactCreate />
        </MainSectionLayout>
    );
};

ContactCreatePage.getLayout = function getLayout(page) {
    return <Layout>{page}</Layout>;
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
    const messages = await loadCatalog(locale as string);

    return {
        props: {
            i18n: messages,
        },
    };
};

export default ContactCreatePage;
