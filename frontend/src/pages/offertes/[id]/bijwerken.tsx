import { DisciplineJsonldDisciplineReadIdentifierEnum } from '@/api/generated';
import Layout from '@/components/layout/Layout';
import { QuotationEditPage as PainterQuotationEditPage } from '@/components/pages/quotationEdit/painter/QuotationEditPage';
import { QuotationEditPage as PlastererQuotationEditPage } from '@/components/pages/quotationEdit/plasterer/QuotationEditPage';
import { useDisciplineContext } from '@/context/DisciplineContext';
import { NextPageWithLayout } from '@/pages/_app';
import { loadCatalog } from '@/utilities/translations';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';

const QuotationEditPage: NextPageWithLayout = () => {
    const { activeDiscipline } = useDisciplineContext();
    const { query } = useRouter();
    const id = query.id as string;

    if (!activeDiscipline) {
        return null;
    }

    if (
        activeDiscipline.identifier ===
        DisciplineJsonldDisciplineReadIdentifierEnum.Plasterer
    ) {
        return <PlastererQuotationEditPage id={id} />;
    }

    if (
        activeDiscipline.identifier ===
        DisciplineJsonldDisciplineReadIdentifierEnum.Painter
    ) {
        return <PainterQuotationEditPage id={id} />;
    }

    return null;
};

QuotationEditPage.getLayout = function getLayout(page) {
    return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
    const messages = await loadCatalog(locale as string);

    return {
        props: {
            i18n: messages,
        },
    };
};

export default QuotationEditPage;
