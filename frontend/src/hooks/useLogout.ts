import { postLogout } from '@/api/repository/loginCheck';
import { AuthContext } from '@/context/AuthContext';
import { disciplineSessionStorageKey } from '@/context/DisciplineContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useContext } from 'react';

export const useLogout = () => {
    const { setAuth } = useContext(AuthContext);
    const queryClient = useQueryClient();

    const clearAfterLogout = () => {
        queryClient.clear();
        queryClient.invalidateQueries();
        setAuth(undefined);
        if (typeof window !== 'undefined') {
            sessionStorage.removeItem(disciplineSessionStorageKey);
        }
    };

    const mutate = useMutation({
        mutationFn: postLogout,
        onSuccess: () => {
            clearAfterLogout();
        },
    });

    return { clearAfterLogout, ...mutate };
};
