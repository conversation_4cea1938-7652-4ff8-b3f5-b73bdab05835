import { QuotationUpdateQuotationCommandJsonld } from '@/api/generated';
import { putQuotation } from '@/api/repository/quotation';
import { QueryKeys } from '@/constants/query';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type PutMutateProps = {
    id: string;
    payload: QuotationUpdateQuotationCommandJsonld;
};

export const usePutQuotation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, payload }: PutMutateProps) =>
            putQuotation(id, payload),
        onSuccess: (_data, variables) => {
            queryClient.invalidateQueries({
                queryKey: [QueryKeys.Quotation, { id: variables.id }],
            });
        },
    });
};
