import {
    DisciplineJsonldDisciplineReadIdentifierEnum,
    PlastererQuotationViewQuotationCommandJsonld,
} from '@/api/generated';
import { putPlastererQuotationView } from '@/api/repository/plastererQuotation';
import { QueryKeys } from '@/constants/query';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type MutateProps = {
    id: string;
    payload: PlastererQuotationViewQuotationCommandJsonld;
};

export const usePutPlastererQuotationView = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, payload }: MutateProps) =>
            putPlastererQuotationView(id, payload),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [
                    QueryKeys.Stats,
                    {
                        discipline:
                            DisciplineJsonldDisciplineReadIdentifierEnum.Plasterer,
                    },
                ],
            });
        },
    });
};
