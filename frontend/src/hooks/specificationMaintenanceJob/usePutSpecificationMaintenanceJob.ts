import { SpecificationMaintenanceJobUpdateSpecificationMaintenanceJobCommandJsonld } from '@/api/generated';
import { putSpecificationMaintenanceJob } from '@/api/repository/specificationMaintenanceJob';
import { useMutation } from '@tanstack/react-query';

type PutMutateProps = {
    id: string;
    payload: SpecificationMaintenanceJobUpdateSpecificationMaintenanceJobCommandJsonld;
};

export const usePutSpecificationMaintenanceJob = () => {
    return useMutation({
        mutationFn: ({ id, payload }: PutMutateProps) =>
            putSpecificationMaintenanceJob(id, payload),
    });
};
