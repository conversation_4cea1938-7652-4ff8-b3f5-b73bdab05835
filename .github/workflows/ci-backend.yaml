name: 'CI backend'

on:
    pull_request:
        paths: [ 'backend/**' ]

concurrency:
    group: ${{ github.ref }}-backend
    cancel-in-progress: true

jobs:
    backend_checks:
        name: "Backend checks"
        runs-on: ubuntu-latest
        timeout-minutes: 7

        env:
            APP_ENV: test

        strategy:
            matrix:
                php-version: [ 8.3 ]

        steps:
            # Setup
            -   name: Checkout repository
                uses: actions/checkout@v4

            -   name: "Install PHP"
                uses: "shivammathur/setup-php@v2"
                with:
                    coverage: "none"
                    php-version: "${{ matrix.php-version }}"
                    tools: cs2pr, symfony-cli
                env:
                    COMPOSER_TOKEN: ${{ secrets.FRESHHEADS_SERVICE_ACCOUNT_PAT }}

            -   name: "Install Composer dependencies"
                uses: "ramsey/composer-install@v3"
                with:
                    working-directory: ./backend

            -   name: Install keypair
                run: symfony console lexik:jwt:generate-keypair --skip-if-exists
                working-directory: ./backend

            # Tests
            -   name: Run Composer linter
                run: symfony composer validate --no-check-publish
                if: success() || failure()
                working-directory: ./backend

            -   name: Run container linter
                run: symfony console lint:container
                if: success() || failure()
                working-directory: ./backend

            -   name: Run Doctrine mapping validation
                run: symfony console doctrine:schema:validate --skip-sync
                if: success() || failure()
                working-directory: ./backend

            -   name: Run deptrac
                run: make deptrac
                if: success() || failure()
                working-directory: ./backend

            -   name: Run php-cs-fixer
                run: make php-cs-fixer-dry-run
                if: success() || failure()
                working-directory: ./backend

            -   name: Run phpstan
                run: make phpstan
                if: success() || failure()
                working-directory: ./backend

            -   name: Unused composer packages
                run: make composer-unused
                if: success() || failure()
                working-directory: ./backend

            -   name: Rector
                run: make rector-dry-run
                if: success() || failure()
                working-directory: ./backend

    phpunit:
        name: "PHPUnit tests"
        runs-on: ubuntu-latest
        timeout-minutes: 10

        env:
            APP_ENV: test

        strategy:
            matrix:
                php-version: [ 8.3 ]

        steps:
            -   name: Checkout repository
                uses: actions/checkout@v4

            -   name: "Install PHP"
                uses: "shivammathur/setup-php@v2"
                with:
                    coverage: "none"
                    php-version: "${{ matrix.php-version }}"
                    tools: cs2pr, symfony-cli
                env:
                    COMPOSER_TOKEN: ${{ secrets.FRESHHEADS_SERVICE_ACCOUNT_PAT }}

            -   name: Install wkhtmltopdf
                run: |
                    sudo apt-get update
                    sudo apt-get install -y wkhtmltopdf
                    sudo ln -sf /usr/bin/wkhtmltopdf /usr/local/bin/wkhtmltopdf

            -   name: "Install Composer dependencies"
                uses: "ramsey/composer-install@v3"
                with:
                    working-directory: ./backend

            -   name: Install keypair
                run: symfony console lexik:jwt:generate-keypair --skip-if-exists
                working-directory: ./backend

            -   name: Run phpunit
                run: make phpunit
                if: success() || failure()
                working-directory: ./backend

            -   name: Dump logs
                run: cat var/log/test.log
                if: success() || failure()
                working-directory: ./backend
