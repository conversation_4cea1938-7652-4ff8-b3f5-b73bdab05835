import { getMaterialUnits } from '@/api/repository/materialUnit';
import { QueryKeys } from '@/constants/query';
import { useQuery } from '@tanstack/react-query';

export const useGetMaterialUnits = () => {
    return useQuery({
        queryKey: [QueryKeys.MaterialUnits],
        queryFn: () => getMaterialUnits({}),
        select: (data) => data['hydra:member'],
        gcTime: 1000 * 60 * 60 * 24,
        staleTime: 1000 * 60 * 60 * 24,
    });
};
