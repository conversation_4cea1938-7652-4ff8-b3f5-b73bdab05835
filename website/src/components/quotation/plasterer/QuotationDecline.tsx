'use client';

import { PlastererQuotationJsonldOpenApiReadStatusEnum } from '@/api/generated';
import { MailLink } from '@/components/contactLink/MailLink';
import { PhoneLink } from '@/components/contactLink/PhoneLink';
import Loader from '@/components/loader/Loader';
import { QuotationDeclineForm } from '@/components/quotation/shared/QuotationDeclineForm';
import { QuotationDeclineFormSchemaFormType } from '@/components/quotation/shared/quotationDeclineSchema';
import { useGetPlastererQuotation } from '@/hooks/plastererQuotation/useGetPlastererQuotation';
import { usePutPlastererQuotationCancel } from '@/hooks/plastererQuotation/usePutPlastererQuotationCancel';
import { useToastPrimitive } from '@/hooks/useToastPrimitive';
import { createQuotationDeclineConfirmationPath } from '@/lib/routes';
import { Container, Heading, Text, VStack } from '@chakra-ui/react';
import { notFound, useRouter } from 'next/navigation';
import { FC } from 'react';

type Props = {
    id: string;
};

export const QuotationDecline: FC<Props> = ({ id }) => {
    const { push } = useRouter();
    const { data, isPending } = useGetPlastererQuotation(id);
    const { mutateAsync, isPending: isPendingMutate } =
        usePutPlastererQuotationCancel();
    const { showToast } = useToastPrimitive();

    const onSubmit = async (data: QuotationDeclineFormSchemaFormType) => {
        try {
            await mutateAsync({
                id,
                payload: { ...data, quotationId: id },
            });

            push(createQuotationDeclineConfirmationPath(id));
        } catch (error) {
            showToast({
                status: 'error',
                title: 'Er ging iets mis',
                description: 'Probeer het later opnieuw',
            });
        }
    };

    if (isPending) {
        return (
            <Container py={'lg'} maxW={'container.lg'}>
                <Loader />
            </Container>
        );
    }

    if (!data) {
        return notFound();
    }

    if (
        data.status === PlastererQuotationJsonldOpenApiReadStatusEnum.Accepted
    ) {
        return (
            <Container py={'lg'} maxW={'container.lg'}>
                <VStack gap={'sm'} alignItems={'stretch'}>
                    <Heading>Deze offerte is al goedgekeurd</Heading>

                    <Text>
                        Heb je nog vragen hierover? Neem gerust contact met ons
                        op via <MailLink /> of bel met <PhoneLink />
                    </Text>
                </VStack>
            </Container>
        );
    }

    if (
        data.status === PlastererQuotationJsonldOpenApiReadStatusEnum.Cancelled
    ) {
        return (
            <Container py={'lg'} maxW={'container.lg'}>
                <VStack gap={'sm'} alignItems={'stretch'}>
                    <Heading>Deze offerte is al geannuleerd</Heading>

                    <Text>
                        Heb je nog vragen hierover? Neem gerust contact met ons
                        op via <MailLink /> of bel met <PhoneLink />
                    </Text>
                </VStack>
            </Container>
        );
    }

    return (
        <Container py={'lg'} maxW={'container.lg'}>
            <VStack gap={'sm'} alignItems={'stretch'}>
                <Heading>Jammer dat je geen interesse meer hebt</Heading>

                <Text>
                    Het spijt ons te horen dat je deze offerte niet wilt
                    goedkeuren. We willen graag leren en verbeteren, dus we
                    horen graag wat we beter hadden kunnen doen. Deel hieronder
                    je feedback of reden.
                </Text>

                <QuotationDeclineForm
                    onSubmit={onSubmit}
                    isPending={isPendingMutate}
                />
            </VStack>
        </Container>
    );
};
