.container {
    border-top: 1px solid var(--chakra-colors-gray-200);
    font-size: var(--chakra-fontSizes-md);
}

.footer-painter {
    border-top: 0;
    background-color: var(--color-accent);
    color: var(--color-accent-accent);
}

.footer-plasterer {
    border-top: 0;
    background-color: var(--color-accent-50);
}

.footerMain {
    padding-block-start: var(--space-3xl);
    padding-block-end: var(--space-3xl);
}

.footerInner {
    --columns: 1;

    display: grid;
    gap: var(--space-xl);
    grid-template-columns: repeat(var(--columns), minmax(0, 1fr));
    align-items: end;
}

.footer-painter .footerBottom {
    background-color: var(--color-accent-800);
    color: var(--color-accent-accent);
}

.footer-plasterer .footerBottom {
    background-color: var(--color-accent-800);
    color: var(--color-accent-accent);
}

.footerBottom {
    background-color: var(--color-primary);
    color: var(--color-primary-accent);
    padding-block-start: var(--space-md);
    padding-block-end: var(--space-md);
}

.footerBottomInner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: var(--space-sm);
}

.footerMain p,
.footerBottomInner p {
    font-size: inherit;
}

@media screen and (min-width: 48em) {
    .footerInner {
        --columns: 2;
    }

    .footerBottomInner {
        flex-direction: row;
        align-items: center;
        gap: var(--space-lg);
    }
}

@media screen and (min-width: 80em) {
    .footerInner {
        --columns: 3;
    }
}
