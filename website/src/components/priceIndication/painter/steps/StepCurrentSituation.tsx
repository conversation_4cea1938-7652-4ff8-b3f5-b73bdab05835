import { CurrentSituationForm } from '@/components/priceIndication/painter/forms/CurrentSituationForm';
import { StepCount } from '@/components/priceIndication/shared/StepCount';
import { usePriceIndicationPainterStepsContext } from '@/context/PriceIndicationPainterStepsContext';
import { Heading, Text, VStack } from '@chakra-ui/react';
import { FC } from 'react';

export const StepCurrentSituation: FC = () => {
    const { stepIndex, steps } = usePriceIndicationPainterStepsContext();

    return (
        <VStack alignItems={'stretch'} gap={'xs'}>
            <StepCount stepIndex={stepIndex} stepsLength={steps.length} />
            <Heading size={'xl'} mt={'sm'}>
                Huidige situatie
            </Heading>
            <Text mb={'md'}>
                Beantwoord onderstaande vragen, zo kunnen wij een goede
                inschatting maken.
            </Text>

            <CurrentSituationForm />
        </VStack>
    );
};
