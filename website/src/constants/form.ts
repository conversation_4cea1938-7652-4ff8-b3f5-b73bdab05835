import '@/lib/zod';
import { z } from 'zod';

export const zStringRequired = z.string().trim().min(1);

const DutchPostalCodePattern = /^[1-9]\d{3} ?(?!sa|sd|ss)[a-z]{2}$/i;
export const zDutchPostalCode = z
    .string()
    .trim()
    .min(1)
    .regex(DutchPostalCodePattern, `Geen geldige postcode`);

export const addressSchema = z.object({
    postalCode: zDutchPostalCode,
    houseNumber: zStringRequired,
    houseNumberSuffix: z
        .string()
        .trim()
        .max(10, `Huisnummertoevoeging is te lang`),
    street: z.string().trim().min(1),
    city: z.string().trim().min(1),
});

export const zBoolTrue = (message = `Dit moet geaccepteerd zijn`) => {
    return z.literal(true, {
        errorMap: () => ({ message }),
    });
};

export const zConstructionYear = z
    .string()
    .trim()
    .length(4, `Bouwjaar moet 4 cijfers bevatten`)
    .regex(/^\d{4}$/, `Bouwjaar moet 4 cijfers bevatten`);

export const zEmail = z.string().trim().min(1).email(`Ongeldig e-mailadres`);

export const zImageUploadSchema = z.object(
    {
        fileUrl: z.string().trim().min(1),
        fileIri: z.string().trim().min(1),
    },
    { message: `Dit veld is verplicht` }
);

const DutchPhonePattern = /^(?:\+31|0)\d{9,10}$/;
export const zPhoneNumber = z.string().trim().min(1).regex(DutchPhonePattern, {
    message: `Geen geldig telefoonnummer`,
});

export const zNumberPositiveRequired = z.number().nonnegative().min(0);

export const zNumberPositiveOptional = z
    .union([zNumberPositiveRequired, z.nan()])
    .optional();
