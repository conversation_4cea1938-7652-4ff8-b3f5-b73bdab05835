import {
    DisciplineIdentifierEnum,
    WebsiteSettingsJsonldWebsiteSettingsRead,
} from '@/api/generated';
import { getWebsiteSettingsCollection } from '@/api/repository/websiteSettings';
import { aboutUsId } from '@/components/aboutUsBlock/AboutUsBlock';
import { NavItem } from '@/components/header/Header';
import { startPriceIndicationId } from '@/components/startPriceIndication/StartPriceIndication';
import { stepsBlockId } from '@/components/stepsBlock/StepsBlock';
import { uspBlockId } from '@/components/uspBlock/UspBlock';
import { formatPhoneNumberToTelLink } from '@/lib/string';

export const getWebsiteSettings = async (
    subdomain: string | null,
    discipline: string | null
) => {
    if (!subdomain && !discipline) {
        return null;
    }

    let identifier;

    switch (discipline) {
        case DisciplineIdentifierEnum.Painter:
            identifier = 'jouwlokaleschilder';
            break;
        case DisciplineIdentifierEnum.Plasterer:
            identifier = 'jouwlokalestukadoor';
            break;
    }

    try {
        // disciplineIdentifier only works when subdomain is set
        const disciplineIdentifier =
            subdomain && discipline ? discipline : undefined;

        return (
            await getWebsiteSettingsCollection({
                subdomain: subdomain ?? undefined,
                identifier: subdomain ? undefined : identifier,
                disciplineIdentifier: disciplineIdentifier,
            })
        )[0];
    } catch (error) {
        throw new Error(`Failed to get website settings: ${error}`);
    }
};

export const getNavItems = (
    settings: WebsiteSettingsJsonldWebsiteSettingsRead | undefined,
    subdomain: string | null
) => {
    if (!settings) {
        return [];
    }

    const navItems: NavItem[] = [
        { label: 'Aanvraag starten', href: `/#${startPriceIndicationId}` },
    ];

    if (settings.usps && settings.usps.length > 0) {
        navItems.push({ label: 'Voordelen', href: `/#${uspBlockId}` });
    }

    if (settings.steps && settings.steps.length > 0) {
        navItems.push({ label: 'Werkwijze', href: `/#${stepsBlockId}` });
    }

    if (settings.aboutText) {
        navItems.push({ label: 'Over ons', href: `/#${aboutUsId}` });
    }

    if (settings.contactPhone) {
        navItems.push({
            label: settings.contactPhone,
            href: formatPhoneNumberToTelLink(settings.contactPhone),
            icon: 'phone-regular',
            sub: subdomain ? undefined : 'ma-vr 9:00-17:00',
        });
    }

    return navItems;
};
