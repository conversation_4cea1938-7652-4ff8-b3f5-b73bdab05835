# ------------------------------------------------------
# https://docs.platform.sh/configuration/app.html
# ------------------------------------------------------

# https://docs.platform.sh/configuration/app/name.html
name: website

# https://docs.platform.sh/configuration/app/storage.html#disk
# Disc size in MB. Start small, storage isn't free
disk: 1536

# Any type will work. There is no "plain HTML" type.
type: 'nodejs:18'

# https://docs.platform.sh/languages/nodejs.html#dependencies
# https://docs.platform.sh/configuration/app/build.html#build
build:
    flavor: none

# https://docs.platform.sh/configuration/app/variables.html
variables:
    env:
        # https://docs.platform.sh/languages/nodejs/nvm.html#how-to-use-nvm-to-run-different-versions-of-nodejs
        NVM_VERSION: v0.39.2
        NODE_VERSION: v18

# https://docs.platform.sh/create-apps/flexible-resources.html
size: XS # 0.25 CPU

# memory = base_memory + (memory_ratio * CPU) = 400 + (256 * 0.25) = 464MB
resources:
    base_memory: 400
    memory_ratio: 256

# https://docs.platform.sh/create-apps/app-reference.html#hooks
# The hooks that will be triggered when the package is deployed.
hooks:
    # https://docs.platform.sh/create-apps/app-reference.html#build-hook
    # No services are available but the disk is writeable.
    build: |
        set -e
        npm pkg delete scripts.prepare
        npm ci
        npm run build
        # Keep build files out of reach
        mkdir -p .build/.next
        mv .next/* .build/.next
    # The deploy hook runs after your application has been deployed and started.
    # Code cannot be modified at this point but the database is available.
    # The site is not accepting requests while this script runs so keep it
    # fast.
    deploy: |
        set -x -e

        # Move stored files back
        rm -rf .next/*
        cp -r .build/.next/* .next

        # relink image cache dir
        rm -f .next/cache/images
        ln -s ../../.next-image-cache .next/cache/images

        # restart node
        systemctl --user restart app

# The configuration of the application when it is exposed to the web.
web:
    commands:
        start: npx next start -p $PORT

# The mounts that will be performed when the package is deployed.
mounts:
    # Next.js will try to cache files, so it must be writeable.
    '/.next':
        source: local
        source_path: 'next'
    '/.next-image-cache':
        source: local
        source_path: 'next-image-cache'
    '/.npm':
        source: local
        source_path: 'npm'
