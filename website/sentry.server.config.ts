// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/
import * as Sentry from '@sentry/nextjs';

const TRACES_SAMPLE_RATE = parseInt(
    process.env.SENTRY_TRACES_SAMPLE_RATE || '0.0'
);

Sentry.init({
    dsn: process.env.SENTRY_DSN_FRONTEND,
    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: TRACES_SAMPLE_RATE,
    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
    // Uncomment the line below to enable Spotlight (https://spotlightjs.com)
    // spotlight: process.env.NODE_ENV === 'development',
});
