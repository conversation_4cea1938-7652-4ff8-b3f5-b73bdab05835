import { withSentryConfig } from '@sentry/nextjs';
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
    webpack(config) {
        config.module.rules.push({
            test: /\.svg$/i,
            issuer: /\.[jt]sx?$/,
            use: ['@svgr/webpack'],
        });

        return config;
    },
    images: {
        formats: ['image/webp'],
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'vakman.planbuilding.wip',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'develop-sr3snxi-uoqukhx6hsyvs.de-2.platformsh.site',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname:
                    'platform-staging-n65s5aa-uoqukhx6hsyvs.de-2.platformsh.site',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname:
                    'platform-demo-3qfyyai-uoqukhx6hsyvs.de-2.platformsh.site',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'vakman.planbuilding.nl',
                pathname: '/**',
            },
        ],
    },
};

export default withSentryConfig(nextConfig, {
    org: 'freshheads-eu',
    project: 'planbuilding-frontend',

    // Only print logs for uploading source maps in CI
    silent: true,

    // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    // tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,
});
